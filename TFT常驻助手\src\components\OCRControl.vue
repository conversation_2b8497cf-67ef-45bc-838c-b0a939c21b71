<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 定义组件的 props 和 emits
interface Props {
  isMinimized?: boolean
  externalPythonRunning?: boolean
  externalAutoMode?: boolean
  externalIsLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isMinimized: false,
  externalPythonRunning: undefined,
  externalAutoMode: undefined,
  externalIsLoading: undefined
})

// 定义 emits
const emit = defineEmits<{
  'status-update': [status: {
    pythonRunning: boolean
    autoMode: boolean
    isLoading: boolean
    statusMessage: string
    ocrStatus: any
  }]
  'toggle-floating-window': []
}>()

// 响应式数据
const pythonStatus = ref<{
  success: boolean
  data?: string
  error?: string
}>({ success: false })

const yimiaoJueStatus = ref<{
  running: boolean
  message: string
}>({ running: false, message: '检查中...' })

const ocrStatus = ref<any>({})
const autoMode = ref(false)
const pythonRunning = ref(false)
const isLoading = ref(false)
const statusMessage = ref('正在初始化...')
const isExpanded = ref(false)

// 添加testResult变量，与原YimiaoJueView保持一致
const testResult = ref<{
  success: boolean
  data?: string
  error?: string
}>({ success: false })

// 计算属性 - 智能状态选择
const currentPythonRunning = computed(() => {
  // 如果内部状态为true，优先使用内部状态
  // 如果内部状态为false但外部状态为true，使用外部状态
  return pythonRunning.value || (props.externalPythonRunning === true)
})

const currentAutoMode = computed(() => {
  // 如果程序在运行，智能选择状态
  if (currentPythonRunning.value) {
    // 如果外部状态有明确值，优先使用外部状态（来自悬浮窗的实时状态）
    if (props.externalAutoMode !== undefined) {
      return props.externalAutoMode
    }
    // 否则使用内部状态
    return autoMode.value
  }
  return false
})

const currentIsLoading = computed(() => {
  // 优先使用内部状态
  return isLoading.value || (props.externalIsLoading === true)
})

const statusIcon = computed(() => {
  if (!currentPythonRunning.value) return '🔴'
  return currentAutoMode.value ? '🟢' : '🟡'
})

const statusText = computed(() => {
  if (!currentPythonRunning.value) return '评分未启动'
  return currentAutoMode.value ? '评分自动监测' : '评分手动模式'
})

const statusTextShort = computed(() => {
  if (!currentPythonRunning.value) return '弈'
  return currentAutoMode.value ? '弈' : '弈'
})

// 发送状态更新到父组件 - 使用内部状态避免循环依赖
const emitStatusUpdate = () => {
  emit('status-update', {
    pythonRunning: pythonRunning.value,
    autoMode: autoMode.value,
    isLoading: isLoading.value,
    statusMessage: statusMessage.value,
    ocrStatus: ocrStatus.value
  })
}

// 检查Python环境
const checkPythonEnvironment = async () => {
  try {
    statusMessage.value = '检查运行环境...'
    const result = await invoke('check_python_environment')
    pythonStatus.value = result as any
    
    if (pythonStatus.value.success) {
      statusMessage.value = '环境检查完成'
    } else {
      statusMessage.value = '环境检查失败'
    }
  } catch (error) {
    console.error('检查Python环境失败:', error)
    pythonStatus.value = {
      success: false,
      error: `检查失败: ${error}`
    }
    statusMessage.value = '环境检查失败'
  }
}

// 检查YimiaoJue状态
const checkYimiaoJueStatus = async () => {
  try {
    statusMessage.value = '检查评分系统状态...'
    const result = await invoke('check_yimiaojue_status')
    yimiaoJueStatus.value = result as any
    
    if (yimiaoJueStatus.value.running) {
      statusMessage.value = '评分系统就绪'
    } else {
      statusMessage.value = '评分系统未就绪'
    }
  } catch (error) {
    console.error('检查弈秒决状态失败:', error)
    yimiaoJueStatus.value = {
      running: false,
      message: `检查失败: ${error}`
    }
    statusMessage.value = '评分系统状态检查失败'
  }
}

// 检查Python进程状态
const checkPythonProcessStatus = async () => {
  try {
    const result = await invoke('check_python_process_status')
    const statusResult = result as any
    console.log('📊 Python进程状态:', statusResult)
    return statusResult
  } catch (error) {
    console.error('检查Python进程状态失败:', error)
    return null
  }
}

// 启动弈秒决
const startYimiaoJue = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '检查现有进程...'

    // 首先检查是否已有进程在运行
    const processStatus = await checkPythonProcessStatus()
    if (processStatus && processStatus.data) {
      console.log('📊 当前进程状态:', processStatus.data)

      // 如果有进程在运行，先清理
      statusMessage.value = '清理现有进程...'
      try {
        await invoke('cleanup_python_processes')
        console.log('✅ 现有进程已清理')
        // 等待一下确保进程完全关闭
        await new Promise(resolve => setTimeout(resolve, 2000))
      } catch (cleanupError) {
        console.warn('⚠️ 清理进程时出现警告:', cleanupError)
      }
    }

    statusMessage.value = '启动识别系统...'
    const result = await invoke('start_yimiaojue')
    const startResult = result as any

    if (startResult.success) {
      statusMessage.value = '评分系统启动成功'
      pythonRunning.value = true
      emitStatusUpdate()

      // 等待3秒让Python程序完全启动，然后获取状态
      setTimeout(async () => {
        await getOCRStatus()
      }, 3000)

      // 重新检查状态
      await checkYimiaoJueStatus()
    } else {
      statusMessage.value = '评分系统启动失败'
      pythonRunning.value = false
      emitStatusUpdate()
      console.error('启动失败:', startResult.error)
    }
  } catch (error) {
    console.error('启动评分系统失败:', error)
    statusMessage.value = '评分系统启动失败'
    pythonRunning.value = false
  } finally {
    isLoading.value = false
  }
}

// 获取评分系统状态
const getOCRStatus = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动评分系统'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '获取系统状态...'

    const result = await invoke('get_ocr_status')
    ocrStatus.value = result as any

    if (ocrStatus.value.success && ocrStatus.value.data) {
      const statusData = JSON.parse(ocrStatus.value.data)
      autoMode.value = statusData.auto_mode
      statusMessage.value = '系统状态获取成功'
      emitStatusUpdate()
    } else {
      statusMessage.value = '系统状态获取失败'
      emitStatusUpdate()
    }
  } catch (error) {
    console.error('获取系统状态失败:', error)
    ocrStatus.value = {
      success: false,
      error: `获取失败: ${error}`
    }
    statusMessage.value = '系统状态获取失败'
  } finally {
    isLoading.value = false
  }
}

// 切换自动模式
const toggleAutoMode = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动评分系统'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '切换监测模式...'

    const result = await invoke('toggle_auto_mode')
    const toggleResult = result as any

    if (toggleResult.success && toggleResult.data) {
      const responseData = JSON.parse(toggleResult.data)
      autoMode.value = responseData.auto_mode
      statusMessage.value = `自动监测已${responseData.action === 'started' ? '启动' : '停止'}`
      emitStatusUpdate()
    } else {
      statusMessage.value = '切换监测模式失败'
      emitStatusUpdate()
    }
  } catch (error) {
    console.error('切换自动模式失败:', error)
    statusMessage.value = '切换监测模式失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描海克斯
const manualScanHex = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动评分系统'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '正在扫描海克斯...'

    const result = await invoke('manual_scan_hex')
    testResult.value = result as any

    console.log('海克斯扫描结果:', testResult.value)

    if (testResult.value.success) {
      statusMessage.value = '海克斯扫描完成'
    } else {
      statusMessage.value = '海克斯扫描失败'
      console.error('扫描失败详情:', testResult.value)
    }
  } catch (error) {
    console.error('手动扫描海克斯失败:', error)
    testResult.value = {
      success: false,
      error: `扫描失败: ${error}`
    }
    statusMessage.value = '海克斯扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描装备
const manualScanEquipment = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动评分系统'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '正在扫描装备...'

    const result = await invoke('manual_scan_equipment')
    testResult.value = result as any

    console.log('装备扫描结果:', testResult.value)

    if (testResult.value.success) {
      statusMessage.value = '装备扫描完成'
    } else {
      statusMessage.value = '装备扫描失败'
      console.error('扫描失败详情:', testResult.value)
    }
  } catch (error) {
    console.error('手动扫描装备失败:', error)
    testResult.value = {
      success: false,
      error: `扫描失败: ${error}`
    }
    statusMessage.value = '装备扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描果实
const manualScanFruit = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动评分系统'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '正在扫描果实...'

    const result = await invoke('manual_scan_fruit')
    testResult.value = result as any

    console.log('果实扫描结果:', testResult.value)

    if (testResult.value.success) {
      statusMessage.value = '果实扫描完成'
    } else {
      statusMessage.value = '果实扫描失败'
      console.error('扫描失败详情:', testResult.value)
    }
  } catch (error) {
    console.error('手动扫描果实失败:', error)
    testResult.value = {
      success: false,
      error: `扫描失败: ${error}`
    }
    statusMessage.value = '果实扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 自动初始化评分系统
const autoInitializeOCR = async () => {
  console.log('🚀 开始自动初始化评分系统...')

  // 1. 检查Python环境
  await checkPythonEnvironment()

  if (!pythonStatus.value.success) {
    console.error('❌ 环境检查失败，跳过评分系统初始化')
    return
  }

  // 2. 检查评分系统状态
  await checkYimiaoJueStatus()

  // 3. 启动评分系统
  await startYimiaoJue()

  if (pythonRunning.value) {
    // 4. 等待程序完全启动后，启动自动模式
    setTimeout(async () => {
      console.log('🔄 启动自动监测...')
      if (!autoMode.value) {
        await toggleAutoMode()
      }
      // 确保状态同步到父组件
      emitStatusUpdate()
    }, 5000) // 等待5秒确保程序完全启动
  }

  // 立即发送当前状态到父组件
  emitStatusUpdate()
}

// 状态检查定时器
let statusCheckInterval: number | null = null

// 定期检查状态 - 优化频率，减少资源消耗
const startStatusCheck = () => {
  // 清除现有定时器
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
  }

  // 每2秒检查一次状态（高频率确保实时性）
  statusCheckInterval = setInterval(async () => {
    if (pythonRunning.value) {
      try {
        // 静默获取状态，减少日志输出
        const result = await invoke('get_ocr_status')
        const ocrResult = result as any

        if (ocrResult.success && ocrResult.data) {
          const statusData = JSON.parse(ocrResult.data)
          const oldAutoMode = autoMode.value

          autoMode.value = statusData.auto_mode

          // 只有状态改变时才更新UI和发送事件
          if (oldAutoMode !== autoMode.value) {
            console.log('🔄 主窗口状态变化:', {
              from: oldAutoMode ? '自动' : '手动',
              to: autoMode.value ? '自动' : '手动'
            })
            emitStatusUpdate()
          }
        }
      } catch (error) {
        // 静默处理错误，避免控制台spam
        if (pythonRunning.value) {
          pythonRunning.value = false
          emitStatusUpdate()
        }
      }
    }
  }, 2000) // 改为2秒，提供更高的实时性
}

// 停止状态检查
const stopStatusCheck = () => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
    statusCheckInterval = null
  }
}

// 组件挂载时自动初始化
onMounted(async () => {
  await autoInitializeOCR()
  // 启动定期状态检查
  startStatusCheck()
})

// 组件卸载时清理
onBeforeUnmount(() => {
  stopStatusCheck()
})

// 切换展开状态
const toggleExpanded = () => {
  if (props.isMinimized) {
    // 在收起状态下，点击控制悬浮窗显示/隐藏
    emit('toggle-floating-window')
  } else {
    // 在展开状态下，正常切换展开/收起
    isExpanded.value = !isExpanded.value
  }
}
</script>

<template>
  <div class="ocr-control" :class="{ 'minimized': props.isMinimized }">
    <!-- 评分系统状态指示器和触发按钮 -->
    <div
      class="ocr-trigger"
      @click="toggleExpanded"
      :title="statusText"
    >
      <span class="status-icon">{{ statusIcon }}</span>
      <span v-if="!props.isMinimized" class="status-text">{{ statusText }}</span>
      <span v-if="!props.isMinimized" class="expand-icon" :class="{ 'expanded': isExpanded }">▼</span>
    </div>

    <!-- 展开的控制面板 -->
    <div 
      v-show="isExpanded" 
      class="ocr-panel"
      :class="{ 'minimized': props.isMinimized }"
    >
      <!-- 状态信息 -->
      <div class="status-info">
        <div class="status-item">
          <span class="label">状态:</span>
          <span class="value">{{ statusMessage }}</span>
        </div>
        <div v-if="isLoading" class="loading-indicator">
          <div class="spinner"></div>
          <span>{{ statusMessage }}</span>
        </div>
      </div>

      <!-- 快速控制按钮 -->
      <div class="quick-controls">
        <button
          @click="toggleAutoMode"
          class="control-btn"
          :class="{ 'active': autoMode, 'disabled': !pythonRunning }"
          :disabled="isLoading || !pythonRunning"
          :title="autoMode ? '停止自动监测' : '启动自动监测'"
        >
          {{ autoMode ? '🛑' : '▶️' }}
        </button>

        <button
          @click="manualScanHex"
          class="control-btn"
          :disabled="isLoading || !pythonRunning"
          title="扫描海克斯评分"
        >
          🔮
        </button>

        <button
          @click="manualScanEquipment"
          class="control-btn"
          :disabled="isLoading || !pythonRunning"
          title="扫描装备评分"
        >
          ⚔️
        </button>

        <button
          @click="manualScanFruit"
          class="control-btn"
          :disabled="isLoading || !pythonRunning"
          title="扫描果实评分"
        >
          🍎
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ocr-control {
  position: relative;
  z-index: 100;
}

.ocr-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.ocr-trigger:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.status-icon {
  font-size: 1rem;
  line-height: 1;
}

.status-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
}

.status-text-short {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.expand-icon {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.ocr-panel {
  position: fixed;
  top: 60px;
  right: 10px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  min-width: 280px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

/* 收起状态下的弹出窗口定位 */
.ocr-control.minimized .ocr-panel {
  top: 45px;
  right: 5px;
  min-width: 240px;
  z-index: 10000;
}

.ocr-panel.minimized {
  min-width: 200px;
}

.status-info {
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.status-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.status-item .value {
  color: rgba(255, 255, 255, 0.9);
  text-align: right;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.5rem;
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.quick-controls {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.control-btn.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}



.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.disabled {
  opacity: 0.3;
}

/* 收起状态下的样式调整 */
.ocr-control.minimized .ocr-trigger {
  padding: 0.4rem 0.6rem;
  min-width: 36px;
}

.ocr-control.minimized .status-text {
  display: none;
}

.ocr-control.minimized .expand-icon {
  display: none;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .ocr-panel {
    min-width: 240px;
  }

  .quick-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
