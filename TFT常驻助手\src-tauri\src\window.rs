use lazy_static::lazy_static;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;
use tauri::{command, Window, Manager, WebviewUrl, WebviewWindowBuilder};
use crate::python_caller;

#[derive(Serialize, Deserialize, Clone)]
pub struct WindowPosition {
    pub x: i32,
    pub y: i32,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct WindowSize {
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct WindowState {
    pub is_minimized: bool,
    pub position: WindowPosition,
    pub size: WindowSize,
}

// 窗口状态缓存
lazy_static! {
    static ref WINDOW_STATE_CACHE: Mutex<HashMap<String, WindowState>> = Mutex::new(HashMap::new());
    static ref ORIGINAL_WINDOW_SIZE: Mutex<Option<(u32, u32)>> = Mutex::new(None);
    static ref FLOATING_WINDOW_STATE: Mutex<bool> = Mutex::new(false); // 悬浮窗是否已创建
}

// Tauri命令：保存初始窗口尺寸
#[command]
pub async fn save_original_size(window: Window) -> Result<(), String> {
    let current_size = window
        .inner_size()
        .map_err(|e| format!("获取窗口大小失败: {}", e))?;
    
    {
        let mut original_size = ORIGINAL_WINDOW_SIZE
            .lock()
            .map_err(|e| format!("获取锁失败: {}", e))?;
        *original_size = Some((current_size.width, current_size.height));
    }
    
    Ok(())
}

// Tauri命令：切换窗口大小（展开/收起）
#[command]
pub async fn toggle_window_size(window: Window) -> Result<(), String> {
    let current_size = window
        .inner_size()
        .map_err(|e| format!("获取窗口大小失败: {}", e))?;

    // 检查当前是否为收起状态（高度小于等于120px）
    let is_minimized = current_size.height <= 120;

    if is_minimized {
        // 展开窗口 - 恢复到原始尺寸
        let original_size = {
            let original_size_guard = ORIGINAL_WINDOW_SIZE
                .lock()
                .map_err(|e| format!("获取锁失败: {}", e))?;
            *original_size_guard
        };
        
        if let Some((original_width, original_height)) = original_size {
            window
                .set_size(tauri::Size::Physical(tauri::PhysicalSize {
                    width: original_width,
                    height: original_height,
                }))
                .map_err(|e| format!("设置窗口大小失败: {}", e))?;
        } else {
            // 如果没有保存原始尺寸，使用默认值
            window
                .set_size(tauri::Size::Physical(tauri::PhysicalSize {
                    width: 870,
                    height: 1050,
                }))
                .map_err(|e| format!("设置窗口大小失败: {}", e))?;
        }
    } else {
        // 收起窗口 - 收起时可以让宽度变小
        window
            .set_size(tauri::Size::Physical(tauri::PhysicalSize {
                width: 400,  // 收起时的较小宽度
                height: 70, // 
            }))
            .map_err(|e| format!("设置窗口大小失败: {}", e))?;
    }

    Ok(())
}

// Tauri命令：设置窗口位置
#[command]
pub async fn set_window_position(window: Window, x: i32, y: i32) -> Result<(), String> {
    window
        .set_position(tauri::Position::Physical(tauri::PhysicalPosition { x, y }))
        .map_err(|e| format!("设置窗口位置失败: {}", e))?;
    Ok(())
}

// Tauri命令：获取窗口位置
#[command]
pub async fn get_window_position(window: Window) -> Result<WindowPosition, String> {
    let position = window
        .outer_position()
        .map_err(|e| format!("获取窗口位置失败: {}", e))?;

    Ok(WindowPosition {
        x: position.x,
        y: position.y,
    })
}

// Tauri命令：获取窗口大小
#[command]
pub async fn get_window_size(window: Window) -> Result<WindowSize, String> {
    let size = window
        .inner_size()
        .map_err(|e| format!("获取窗口大小失败: {}", e))?;

    Ok(WindowSize {
        width: size.width as f64,
        height: size.height as f64,
    })
}

// Tauri命令：开始拖拽窗口
#[command]
pub async fn start_drag(window: Window) -> Result<(), String> {
    window
        .start_dragging()
        .map_err(|e| format!("开始拖拽失败: {}", e))?;
    Ok(())
}

// Tauri命令：关闭应用程序
#[command]
pub async fn close_app(app: tauri::AppHandle) -> Result<(), String> {
    println!("🚪 开始关闭应用程序...");

    // 首先清理Python进程
    println!("🧹 清理识别程序进程...");
    match python_caller::cleanup_python_processes().await {
        Ok(result) => {
            if result.success {
                println!("✅ 识别程序进程清理成功: {:?}", result.data);
            } else {
                println!("⚠️ 识别程序进程清理部分失败: {:?}", result.error);
            }
        }
        Err(e) => {
            println!("❌ 识别程序进程清理失败: {}", e);
        }
    }

    // 关闭所有窗口
    println!("🚪 关闭所有窗口...");
    if let Some(main_window) = app.get_webview_window("main") {
        let _ = main_window.close();
    }
    if let Some(floating_window) = app.get_webview_window("floating-ocr") {
        let _ = floating_window.close();
    }

    // 延迟一点时间确保清理完成，然后退出应用
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    println!("🚪 退出应用程序...");
    app.exit(0);
    Ok(())
}

// Tauri命令：检查窗口是否为收起状态
#[command]
pub async fn is_window_minimized(window: Window) -> Result<bool, String> {
    let current_size = window
        .inner_size()
        .map_err(|e| format!("获取窗口大小失败: {}", e))?;
    Ok(current_size.height <= 120)
}

// Tauri命令：设置窗口置顶状态
#[command]
pub async fn set_always_on_top(window: Window, always_on_top: bool) -> Result<(), String> {
    window
        .set_always_on_top(always_on_top)
        .map_err(|e| format!("设置窗口置顶失败: {}", e))?;
    Ok(())
}

// Tauri命令：获取窗口状态
#[command]
pub async fn get_window_state(window: Window) -> Result<WindowState, String> {
    let position = get_window_position(window.clone()).await?;
    let size = get_window_size(window.clone()).await?;
    let is_minimized = is_window_minimized(window).await?;

    Ok(WindowState {
        is_minimized,
        position,
        size,
    })
}

// Tauri命令：保存窗口状态到缓存
#[command]
pub async fn save_window_state(window: Window, state_key: String) -> Result<(), String> {
    let state = get_window_state(window).await?;

    {
        let mut cache = WINDOW_STATE_CACHE
            .lock()
            .map_err(|e| format!("获取缓存锁失败: {}", e))?;
        cache.insert(state_key, state);
    }

    Ok(())
}

// Tauri命令：从缓存恢复窗口状态
#[command]
pub async fn restore_window_state(window: Window, state_key: String) -> Result<(), String> {
    let state_option = {
        let cache = WINDOW_STATE_CACHE
            .lock()
            .map_err(|e| format!("获取缓存锁失败: {}", e))?;
        cache.get(&state_key).cloned()
    };

    if let Some(state) = state_option {
        // 恢复窗口位置
        set_window_position(window.clone(), state.position.x, state.position.y).await?;

        // 恢复窗口大小
        window
            .set_size(tauri::Size::Physical(tauri::PhysicalSize {
                width: state.size.width as u32,
                height: state.size.height as u32,
            }))
            .map_err(|e| format!("恢复窗口大小失败: {}", e))?;
    }

    Ok(())
}

// Tauri命令：居中窗口
#[command]
pub async fn center_window(window: Window) -> Result<(), String> {
    window
        .center()
        .map_err(|e| format!("居中窗口失败: {}", e))?;
    Ok(())
}

// Tauri命令：设置窗口最小尺寸
#[command]
pub async fn set_min_size(window: Window, width: f64, height: f64) -> Result<(), String> {
    window
        .set_min_size(Some(tauri::Size::Physical(tauri::PhysicalSize {
            width: width as u32,
            height: height as u32,
        })))
        .map_err(|e| format!("设置最小尺寸失败: {}", e))?;
    Ok(())
}

// Tauri命令：设置窗口最大尺寸
#[command]
pub async fn set_max_size(window: Window, width: f64, height: f64) -> Result<(), String> {
    window
        .set_max_size(Some(tauri::Size::Physical(tauri::PhysicalSize {
            width: width as u32,
            height: height as u32,
        })))
        .map_err(|e| format!("设置最大尺寸失败: {}", e))?;
    Ok(())
}

// ========== 悬浮窗管理功能 ==========

// Tauri命令：创建OCR悬浮窗
#[command]
pub async fn create_floating_ocr_window(app: tauri::AppHandle) -> Result<(), String> {
    // 检查悬浮窗是否已存在
    if let Some(_) = app.get_webview_window("floating-ocr") {
        println!("悬浮窗已存在，直接显示");
        return show_floating_ocr_window(app).await;
    }

    // 获取主窗口位置和大小
    let main_window = app.get_webview_window("main")
        .ok_or("找不到主窗口".to_string())?;

    let main_position = main_window
        .outer_position()
        .map_err(|e| format!("获取主窗口位置失败: {}", e))?;

    let main_size = main_window
        .inner_size()
        .map_err(|e| format!("获取主窗口大小失败: {}", e))?;

    // 创建悬浮窗
    let floating_window = WebviewWindowBuilder::new(
        &app,
        "floating-ocr",
        WebviewUrl::App("floating-ocr.html".into())
    )
    .title("OCR控制")
    .inner_size(260.0, 186.0)  // 140 * 4/3 = 186.67 ≈ 186
    .min_inner_size(200.0, 150.0)
    .max_inner_size(350.0, 250.0)
    .resizable(true)
    .decorations(false)
    .always_on_top(true)
    .skip_taskbar(true)
    .transparent(true)
    .visible(false) // 初始隐藏
    .build()
    .map_err(|e| format!("创建悬浮窗失败: {}", e))?;

    // 计算悬浮窗位置：主窗口左侧对齐，下方一点点距离
    let floating_x = main_position.x;  // 与主窗口左侧对齐
    let floating_y = main_position.y + main_size.height as i32 + 5;  // 主窗口下方5px

    println!("主窗口位置: ({}, {}), 大小: {}x{}", main_position.x, main_position.y, main_size.width, main_size.height);
    println!("悬浮窗位置: ({}, {})", floating_x, floating_y);

    // 设置悬浮窗位置
    floating_window
        .set_position(tauri::Position::Physical(tauri::PhysicalPosition {
            x: floating_x,
            y: floating_y
        }))
        .map_err(|e| format!("设置悬浮窗位置失败: {}", e))?;

    // 更新状态
    {
        let mut state = FLOATING_WINDOW_STATE
            .lock()
            .map_err(|e| format!("获取锁失败: {}", e))?;
        *state = true;
    }

    println!("悬浮窗创建成功");
    Ok(())
}

// Tauri命令：显示OCR悬浮窗
#[command]
pub async fn show_floating_ocr_window(app: tauri::AppHandle) -> Result<(), String> {
    if let Some(floating_window) = app.get_webview_window("floating-ocr") {
        // 重新计算并设置悬浮窗位置
        if let Some(main_window) = app.get_webview_window("main") {
            let main_position = main_window
                .outer_position()
                .map_err(|e| format!("获取主窗口位置失败: {}", e))?;

            let main_size = main_window
                .inner_size()
                .map_err(|e| format!("获取主窗口大小失败: {}", e))?;

            // 计算悬浮窗位置：主窗口左侧对齐，下方一点点距离
            let floating_x = main_position.x;  // 与主窗口左侧对齐
            let floating_y = main_position.y + main_size.height as i32 + 5;  // 主窗口下方5px

            println!("更新悬浮窗位置: ({}, {})", floating_x, floating_y);

            // 设置悬浮窗位置
            floating_window
                .set_position(tauri::Position::Physical(tauri::PhysicalPosition {
                    x: floating_x,
                    y: floating_y
                }))
                .map_err(|e| format!("设置悬浮窗位置失败: {}", e))?;
        }

        floating_window
            .show()
            .map_err(|e| format!("显示悬浮窗失败: {}", e))?;
        floating_window
            .set_focus()
            .map_err(|e| format!("聚焦悬浮窗失败: {}", e))?;
        println!("悬浮窗已显示");
    } else {
        return Err("悬浮窗不存在，请先创建".to_string());
    }
    Ok(())
}

// Tauri命令：隐藏OCR悬浮窗
#[command]
pub async fn hide_floating_ocr_window(app: tauri::AppHandle) -> Result<(), String> {
    if let Some(floating_window) = app.get_webview_window("floating-ocr") {
        floating_window
            .hide()
            .map_err(|e| format!("隐藏悬浮窗失败: {}", e))?;
        println!("悬浮窗已隐藏");
    }
    Ok(())
}

// Tauri命令：切换OCR悬浮窗显示状态
#[command]
pub async fn toggle_floating_ocr_window(app: tauri::AppHandle) -> Result<bool, String> {
    if let Some(floating_window) = app.get_webview_window("floating-ocr") {
        let is_visible = floating_window
            .is_visible()
            .map_err(|e| format!("获取悬浮窗可见性失败: {}", e))?;

        if is_visible {
            hide_floating_ocr_window(app).await?;
            Ok(false)
        } else {
            show_floating_ocr_window(app).await?;
            Ok(true)
        }
    } else {
        // 如果悬浮窗不存在，先创建再显示
        create_floating_ocr_window(app.clone()).await?;
        show_floating_ocr_window(app).await?;
        Ok(true)
    }
}

// Tauri命令：关闭OCR悬浮窗
#[command]
pub async fn close_floating_ocr_window(app: tauri::AppHandle) -> Result<(), String> {
    if let Some(floating_window) = app.get_webview_window("floating-ocr") {
        floating_window
            .close()
            .map_err(|e| format!("关闭悬浮窗失败: {}", e))?;

        // 更新状态
        {
            let mut state = FLOATING_WINDOW_STATE
                .lock()
                .map_err(|e| format!("获取锁失败: {}", e))?;
            *state = false;
        }

        println!("悬浮窗已关闭");
    }
    Ok(())
}

// Tauri命令：检查悬浮窗是否存在
#[command]
pub async fn is_floating_ocr_window_exists(app: tauri::AppHandle) -> Result<bool, String> {
    Ok(app.get_webview_window("floating-ocr").is_some())
}
