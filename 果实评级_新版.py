import json
import asyncio
import random
import os
import argparse
from pathlib import Path
from datetime import datetime
from playwright.async_api import async_playwright
from datetime import datetime

def setup_directories():
    """设置数据目录结构"""
    data_dir = Path("果实数据")
    mapping_dir = Path("映射文件")

    data_dir.mkdir(exist_ok=True)
    mapping_dir.mkdir(exist_ok=True)

    return data_dir, mapping_dir

def clean_existing_data(data_dir, mapping_dir):
    """清理现有数据，重新爬取"""
    import shutil

    # 删除果实数据文件夹内容
    if data_dir.exists():
        shutil.rmtree(data_dir)
        data_dir.mkdir(exist_ok=True)

    # 删除映射文件
    mapping_file = mapping_dir / "果实映射.json"
    if mapping_file.exists():
        mapping_file.unlink()

    print("[清理] 已清理现有数据，准备重新爬取")

def check_existing_data(data_dir, mapping_dir):
    """检查是否已有数据，支持快速开始"""
    mapping_file = mapping_dir / "果实映射.json"
    if mapping_file.exists():
        try:
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping = json.load(f)

            # 检查数据文件是否完整
            missing_files = []
            for hero_info in mapping:
                hero_file = data_dir / hero_info['file_name']
                if not hero_file.exists():
                    missing_files.append(hero_info['hero_name'])

            if not missing_files:
                print(f"[信息] 发现完整的现有数据，共 {len(mapping)} 个英雄")
                return True, mapping
            else:
                print(f"[警告] 发现 {len(missing_files)} 个英雄数据文件缺失")
                return False, None
        except Exception as e:
            print(f"[警告] 读取现有数据失败: {e}")
            return False, None

    return False, None

def parse_hero_name_and_type(full_name):
    """拆分英雄名称和定位"""
    if " - " in full_name:
        parts = full_name.split(" - ")
        hero_name = parts[0].strip()
        hero_type_en = parts[1].strip() if len(parts) > 1 else "Unknown"
        # 英雄类型汉化
        hero_type = translate_hero_type(hero_type_en)
    else:
        hero_name = full_name.strip()
        hero_type = "未知"

    return hero_name, hero_type

def translate_hero_type(hero_type_en):
    """英雄类型汉化"""
    type_mapping = {
        "Tank": "坦克",
        "Fighter": "战士",
        "Marksman": "射手",
        "Caster": "施法者",
        "Specialist": "专家",
        "Assassin": "刺客",
        "Support": "辅助"
    }
    return type_mapping.get(hero_type_en, hero_type_en)

async def extract_traits(champ_el):
    """提取英雄羁绊信息"""
    traits = []
    try:
        # 根据你提供的HTML结构，羁绊在 .traits > .trait 中
        trait_elements = await champ_el.query_selector_all(".traits .trait")
        for trait_el in trait_elements:
            try:
                # 羁绊名称直接在trait元素的文本内容中
                trait_text = await trait_el.inner_text()
                if trait_text:
                    trait_text = trait_text.strip()
                    # 过滤掉空白和无效内容
                    if trait_text and len(trait_text) > 1:
                        traits.append(trait_text)
            except:
                continue
    except:
        pass

    return traits

async def extract_powerup_stage_info(powerup_el):
    """提取果实的阶段限制信息"""
    stage_info = {}

    try:
        # 查找 extras type-overline 元素
        extras_el = await powerup_el.query_selector('.extras.type-overline')
        if extras_el:
            # 提取最小阶段
            min_stage_el = await extras_el.query_selector('.min-stage')
            if min_stage_el:
                min_stage_text = await min_stage_el.inner_text()
                # 提取数字，例如 "Min Stage: 3" -> "3"
                import re
                min_stage_match = re.search(r'(\d+)', min_stage_text)
                if min_stage_match:
                    stage_info['min_stage'] = int(min_stage_match.group(1))

            # 提取最大阶段
            max_stage_el = await extras_el.query_selector('.max-stage')
            if max_stage_el:
                max_stage_text = await max_stage_el.inner_text()
                # 提取数字，例如 "Max Stage: 4" -> "4"
                import re
                max_stage_match = re.search(r'(\d+)', max_stage_text)
                if max_stage_match:
                    stage_info['max_stage'] = int(max_stage_match.group(1))

            # 提取权重信息
            weight_el = await extras_el.query_selector('.stacking-bonus')
            if weight_el:
                weight_text = await weight_el.inner_text()
                # 提取数字，例如 "Weight: 10" -> "10"
                import re
                weight_match = re.search(r'(\d+)', weight_text)
                if weight_match:
                    stage_info['weight'] = int(weight_match.group(1))
    except Exception as e:
        print(f"[警告] 提取阶段信息时出错: {e}")

    return stage_info

async def scrape_tft_powerups_async(debug_mode=False):
    """完全按照你的成功debug代码逻辑，但使用async版本，添加完整的数据管理功能"""

    # 设置目录
    data_dir, mapping_dir = setup_directories()

    # 默认清除所有数据重新爬取
    print("[信息] 清除现有数据，重新爬取...")
    clean_existing_data(data_dir, mapping_dir)

    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(
            headless=not debug_mode,  # debug模式显示浏览器
            args=['--no-sandbox', '--disable-setuid-sandbox']
        )
        
        # 创建上下文和页面
        context = await browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='zh-CN',
            extra_http_headers={'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'}
        )
        page = await context.new_page()
        
        print("[信息] 正在访问页面...")
        await page.goto("https://blitz.gg/tft/set-15/powerups", wait_until="networkidle")

        # 等待关键元素加载（回到成功版本的等待逻辑）
        print("[信息] 等待页面关键元素加载...")
        await page.wait_for_selector("article .champion-container .champion", timeout=30000)
        print("[信息] 英雄容器加载完成")

        # 反爬机制：随机延迟
        delay = random.uniform(1, 3)
        print(f"[反爬] 随机延迟 {delay:.2f} 秒")
        await asyncio.sleep(delay)
        
        # 1. 点击所有"Show All"（type-form--button）按钮，确保内容都展开
        print("[步骤1] 开始展开所有英雄的果实...")
        EXPAND_BTN = "article .champion-container .champion > button.type-form--button"
        await page.wait_for_selector(EXPAND_BTN)
        buttons = await page.query_selector_all(EXPAND_BTN)
        print(f"[信息] 共找到 {len(buttons)} 个展开按钮，开始点击…")
        
        # 决定要点击的按钮数量
        buttons_to_click = 3 if debug_mode else len(buttons)
        
        for idx in range(buttons_to_click):
            btn = buttons[idx]
            await btn.scroll_into_view_if_needed()
            await btn.click()
            print(f"[信息] 已点击 第 {idx + 1} 个")
            await page.wait_for_timeout(500)  # 等动画/数据加载完成
        
        if debug_mode:
            print("[调试] 调试模式，只展开前3个英雄")
        
        # 2. 等所有展开区域稳定后，抓取所有英雄卡
        print("[步骤2] 等待所有展开区域稳定...")
        await page.wait_for_timeout(1000)
        
        print("[步骤3] 开始解析展开后的果实数据...")
        champions = await page.query_selector_all("article .champion-container .champion")
        print(f"[信息] 找到 {len(champions)} 个英雄")

        all_heroes_data = {}  # 存储所有英雄数据
        mapping_data = []     # 存储映射关系（改为数组格式，适配数据库）

        for i, champ_el in enumerate(champions):
            if i >= 3 and debug_mode:
                print("[调试] 调试模式，只处理前3个英雄")
                break

            # 反爬机制：处理英雄间的随机延迟
            if i > 0:
                delay = random.uniform(0.5, 1.5)
                await asyncio.sleep(delay)

            # 英雄名称 - 尝试多个选择器
            full_hero_name = "Unknown"
            name_selectors = [
                ".powerups .top div:nth-child(2) > h3", 
                ".powerups .top div:nth-child(2) > h4",
                "h3", "h4", ".top h3", ".champion-name"
            ]
            
            for selector in name_selectors:
                try:
                    name_el = await champ_el.query_selector(selector)
                    if name_el:
                        full_hero_name = await name_el.inner_text()
                        full_hero_name = full_hero_name.strip() if full_hero_name else "Unknown"
                        if full_hero_name and full_hero_name != "Unknown":
                            break
                except:
                    continue

            # 拆分英雄名称和定位
            hero_name, hero_type = parse_hero_name_and_type(full_hero_name)
            print(f"[处理] 正在处理英雄: {hero_name} ({hero_type})")

            # 提取羁绊信息
            traits = await extract_traits(champ_el)
            print(f"[羁绊] 英雄 {hero_name} 羁绊: {', '.join(traits) if traits else '无'}")

            # 每个 powerup 分组
            powerups = []
            groups = await champ_el.query_selector_all(".powerups > div")
            print(f"[信息] 英雄 {hero_name} 找到 {len(groups)} 个果实分组")
            
            for grp in groups:
                try:
                    # 标题 (评分 + 名称)
                    title = ""
                    title_selectors = [".powerup-header h3", ".powerup-header h4", "h3", "h4"]
                    
                    for title_selector in title_selectors:
                        try:
                            title_el = await grp.query_selector(title_selector)
                            if title_el:
                                title = await title_el.inner_text()
                                title = title.strip() if title else ""
                                if title:
                                    break
                        except:
                            continue
                    
                    # 详情：ol > li 列表
                    details = []
                    items = await grp.query_selector_all("ol > li")
                    for li in items:
                        try:
                            detail_text = await li.inner_text()
                            if detail_text and detail_text.strip():
                                details.append(detail_text.strip())
                        except:
                            continue
                    
                    # 尝试获取评级信息 - 从图片或其他元素
                    tier_rank = "Unknown"
                    tier_selectors = [
                        "img[src*='tier']", 
                        "img[alt*='tier']",
                        ".tier", 
                        "[class*='tier']",
                        "img[src*='rank']"
                    ]
                    
                    for tier_selector in tier_selectors:
                        try:
                            tier_el = await grp.query_selector(tier_selector)
                            if tier_el:
                                # 尝试从src属性获取评级
                                src = await tier_el.get_attribute('src')
                                alt = await tier_el.get_attribute('alt')
                                
                                for attr in [src, alt]:
                                    if attr:
                                        if 'tier-s' in attr.lower() or 's-tier' in attr.lower():
                                            tier_rank = "S"
                                        elif 'tier-a' in attr.lower() or 'a-tier' in attr.lower():
                                            tier_rank = "A"
                                        elif 'tier-b' in attr.lower() or 'b-tier' in attr.lower():
                                            tier_rank = "B"
                                        elif 'tier-c' in attr.lower() or 'c-tier' in attr.lower():
                                            tier_rank = "C"
                                        elif 'tier-d' in attr.lower() or 'd-tier' in attr.lower():
                                            tier_rank = "D"
                                
                                if tier_rank != "Unknown":
                                    break
                        except:
                            continue
                    
                    # 提取阶段限制信息
                    stage_info = await extract_powerup_stage_info(grp)

                    if title:  # 只有当标题存在时才添加
                        powerup_data = {
                            "powerup_name": title,
                            "tier_rank": tier_rank,
                            "details": details
                        }

                        # 添加阶段信息，但清理默认权重值
                        if stage_info:
                            # 只保留非默认的权重值（不是10的权重）
                            cleaned_stage_info = {}
                            for key, value in stage_info.items():
                                if key == 'weight' and value == 10:
                                    continue  # 跳过默认权重值10
                                cleaned_stage_info[key] = value

                            if cleaned_stage_info:
                                powerup_data.update(cleaned_stage_info)

                        powerups.append(powerup_data)

                        # 打印信息，包含阶段限制（显示时仍显示权重用于调试）
                        stage_text = ""
                        if stage_info:
                            stage_parts = []
                            if 'min_stage' in stage_info:
                                stage_parts.append(f"最小阶段:{stage_info['min_stage']}")
                            if 'max_stage' in stage_info:
                                stage_parts.append(f"最大阶段:{stage_info['max_stage']}")
                            if 'weight' in stage_info and stage_info['weight'] != 10:
                                stage_parts.append(f"权重:{stage_info['weight']}")
                            if stage_parts:
                                stage_text = f" [{', '.join(stage_parts)}]"

                        print(f"[解析] {hero_name} - {title} ({tier_rank}){stage_text}")

                except Exception as e:
                    print(f"[警告] 解析果实分组失败: {e}")
                    continue

            # 构建英雄数据
            hero_data = {
                "hero_name": hero_name,
                "hero_type": hero_type,
                "traits": traits,  # 添加羁绊信息
                "powerups": powerups,
                "last_updated": datetime.now().isoformat(),
                "total_powerups": len(powerups)
            }

            # 保存单个英雄数据文件
            hero_file = data_dir / f"{hero_name}.json"
            with open(hero_file, 'w', encoding='utf-8') as f:
                json.dump(hero_data, f, ensure_ascii=False, indent=2)

            # 添加到映射数据（数组格式，适配数据库）
            mapping_data.append({
                "id": i + 1,  # 数据库ID
                "hero_name": hero_name,
                "hero_type": hero_type,
                "traits": traits,
                "file_name": f"{hero_name}.json",
                "file_path": f"./果实数据/{hero_name}.json",  # 完整路径
                "total_powerups": len(powerups),
                "last_updated": datetime.now().isoformat()
            })

            all_heroes_data[hero_name] = hero_data
            print(f"[保存] 英雄 {hero_name} 数据已保存，共 {len(powerups)} 个果实")

        # 保存映射文件到映射文件文件夹
        mapping_file = mapping_dir / "果实映射.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)

        await browser.close()
        return mapping_data  # 返回映射数据

def generate_summary_report(mapping_data):
    """生成汇总报告"""
    print("\n" + "="*50)
    print("果实评级数据汇总报告")
    print("="*50)
    print(f"总英雄数量: {len(mapping_data)}")

    # 按英雄类型统计
    type_stats = {}
    trait_stats = {}
    total_powerups = 0

    for hero_info in mapping_data:
        hero_type = hero_info['hero_type']
        powerup_count = hero_info['total_powerups']
        traits = hero_info.get('traits', [])

        # 类型统计
        if hero_type not in type_stats:
            type_stats[hero_type] = {'count': 0, 'powerups': 0}

        type_stats[hero_type]['count'] += 1
        type_stats[hero_type]['powerups'] += powerup_count
        total_powerups += powerup_count

        # 羁绊统计
        for trait in traits:
            if trait not in trait_stats:
                trait_stats[trait] = 0
            trait_stats[trait] += 1

    print(f"总果实数量: {total_powerups}")
    print("\n英雄类型分布:")
    for hero_type, stats in sorted(type_stats.items()):
        print(f"  {hero_type}: {stats['count']} 个英雄, {stats['powerups']} 个果实")

    print("\n羁绊分布:")
    for trait, count in sorted(trait_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {trait}: {count} 个英雄")

    print(f"\n数据文件位置: ./果实数据/")
    print(f"映射文件: ./映射文件/果实映射.json")
    print("="*50)

async def main(debug_mode=False):
    """主函数"""
    print("[开始] 果实评级获取程序启动")

    try:
        mapping_data = await scrape_tft_powerups_async(debug_mode)
        if mapping_data:
            print("[完成] 数据爬取完成")
            generate_summary_report(mapping_data)
            return True
        else:
            print("[错误] 未能获取任何数据")
            return False
    except Exception as e:
        print(f"[错误] 程序执行失败: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='TFT果实评级数据爬取工具')
    parser.add_argument('--debug', action='store_true', help='开启调试模式（显示浏览器界面）')
    args = parser.parse_args()

    success = asyncio.run(main(debug_mode=args.debug))
    if success:
        print("[成功] 程序执行完成")
    else:
        print("[失败] 程序执行失败")
