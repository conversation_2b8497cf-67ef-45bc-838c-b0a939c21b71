#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import json
import asyncio
import requests
import aiohttp
import argparse
import sys
from urllib.parse import urlparse, quote
from playwright.async_api import async_playwright
import traceback
import re
import random

# 添加GUI相关依赖
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading

# 获取脚本所在目录的绝对路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 向前声明
run_hero_equipment_scraper = None

# 定义GUI类
class ScraperGUI:
    """
    英雄装备数据爬取工具的图形用户界面
    """
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("云顶之奕英雄装备数据爬取工具")
        self.root.geometry("1200x700")  # 设置窗口大小，宽度1200像素，高度700像素
        
        # 创建样式
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        
        # 爬虫实例
        self.scraper = None
        
        # 爬取线程
        self.scrape_thread = None
        
        # 创建界面元素
        self._create_widgets()
    
    def _create_widgets(self):
        """创建界面元素"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 数据保存目录
        dir_frame = ttk.LabelFrame(main_frame, text="数据保存目录", padding="5")
        dir_frame.pack(fill=tk.X, pady=5)
        
        self.dir_var = tk.StringVar(value=os.path.dirname(os.path.abspath(__file__)))
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=50)
        dir_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(dir_frame, text="浏览...", command=self._browse_dir)
        browse_btn.pack(side=tk.RIGHT, padx=5)
        
        # 爬取选项
        options_frame = ttk.LabelFrame(main_frame, text="爬取选项", padding="5")
        options_frame.pack(fill=tk.X, pady=5)
        
        # 最大并发数
        concurrency_frame = ttk.Frame(options_frame)
        concurrency_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(concurrency_frame, text="最大并发数:").pack(side=tk.LEFT, padx=5)
        self.concurrency_var = tk.IntVar(value=3)
        concurrency_spin = ttk.Spinbox(concurrency_frame, from_=1, to=10, textvariable=self.concurrency_var, width=5)
        concurrency_spin.pack(side=tk.LEFT, padx=5)
        
        # 超时设置
        timeout_frame = ttk.Frame(options_frame)
        timeout_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(timeout_frame, text="超时时间(秒):").pack(side=tk.LEFT, padx=5)
        self.timeout_var = tk.IntVar(value=300)
        timeout_spin = ttk.Spinbox(timeout_frame, from_=0, to=3600, textvariable=self.timeout_var, width=5)
        timeout_spin.pack(side=tk.LEFT, padx=5)
        ttk.Label(timeout_frame, text="(0表示无超时限制)").pack(side=tk.LEFT, padx=5)
        
        # 添加随机延迟
        delay_frame = ttk.Frame(options_frame)
        delay_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(delay_frame, text="随机延迟(秒):").pack(side=tk.LEFT, padx=5)
        self.min_delay_var = tk.DoubleVar(value=0.5)
        self.max_delay_var = tk.DoubleVar(value=1.0)
        
        ttk.Label(delay_frame, text="最小值:").pack(side=tk.LEFT, padx=5)
        min_delay_spin = ttk.Spinbox(delay_frame, from_=0.0, to=5.0, increment=0.1, textvariable=self.min_delay_var, width=5)
        min_delay_spin.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(delay_frame, text="最大值:").pack(side=tk.LEFT, padx=5)
        max_delay_spin = ttk.Spinbox(delay_frame, from_=0.5, to=10.0, increment=0.1, textvariable=self.max_delay_var, width=5)
        max_delay_spin.pack(side=tk.LEFT, padx=5)
        
        # 爬取内容选项
        content_frame = ttk.LabelFrame(options_frame, text="爬取内容", padding="5")
        content_frame.pack(fill=tk.X, pady=5)
        
        # 第一行选项
        first_row = ttk.Frame(content_frame)
        first_row.pack(fill=tk.X, pady=5)
        
        self.fetch_heroes_var = tk.BooleanVar(value=False)
        fetch_heroes_cb = ttk.Checkbutton(first_row, text="爬取英雄列表与基础信息", variable=self.fetch_heroes_var)
        fetch_heroes_cb.pack(side=tk.LEFT, padx=5)
        
        self.fetch_icons_var = tk.BooleanVar(value=False)
        fetch_icons_cb = ttk.Checkbutton(first_row, text="爬取英雄图标", variable=self.fetch_icons_var)
        fetch_icons_cb.pack(side=tk.LEFT, padx=5)
        
        # 第二行选项
        second_row = ttk.Frame(content_frame)
        second_row.pack(fill=tk.X, pady=5)
        
        self.fetch_equips_var = tk.BooleanVar(value=True)
        fetch_equips_cb = ttk.Checkbutton(second_row, text="爬取英雄装备数据", variable=self.fetch_equips_var)
        fetch_equips_cb.pack(side=tk.LEFT, padx=5)
        
        # 添加高级选项
        advanced_frame = ttk.LabelFrame(options_frame, text="高级选项", padding="5")
        advanced_frame.pack(fill=tk.X, pady=5)
        
        # 添加使用最新数据选项
        self.use_latest_var = tk.BooleanVar(value=True)
        use_latest_cb = ttk.Checkbutton(advanced_frame, text="使用最新数据 (/latest)", variable=self.use_latest_var)
        use_latest_cb.pack(side=tk.LEFT, padx=5)
        
        # 添加分隔线
        separator = ttk.Separator(options_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=5)
        
        # 操作按钮
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        self.start_button = ttk.Button(buttons_frame, text="开始爬取", command=self._start_scraping)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(buttons_frame, text="停止爬取", command=self._stop_scraping, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        clear_btn = ttk.Button(buttons_frame, text="清理所有数据", command=self._clear_all_data)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 日志显示区
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建日志文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, width=70, wrap=tk.WORD)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        
        # 进度显示
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5)
        
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.pack(pady=5)
        
        # 重定向标准输出到日志框
        self._redirect_stdout()
    
    def _clear_all_data(self):
        """清理所有数据"""
        if messagebox.askyesno("确认清理", "确定要清理所有已爬取的数据吗？这将删除所有爬取的文件和映射！"):
            try:
                # 运行清理任务
                threading.Thread(target=self._run_clean_all, daemon=True).start()
            except Exception as e:
                messagebox.showerror("清理出错", f"清理数据时出错：\n{str(e)}")
                
    def _run_clean_all(self):
        """运行清理所有数据的任务"""
        # 更新状态
        self.status_var.set("正在清理数据...")
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        base_dir = self.dir_var.get()
        
        # 清理英雄图标
        icon_dir = os.path.join(base_dir, "英雄图标")
        if os.path.exists(icon_dir):
            try:
                self._delete_directory_contents(icon_dir)
                print(f"已清理英雄图标目录: {icon_dir}")
            except Exception as e:
                print(f"清理英雄图标目录时出错: {str(e)}")
        
        # 清理装备数据
        equips_dir = os.path.join(base_dir, "英雄装备数据")
        if os.path.exists(equips_dir):
            try:
                self._delete_directory_contents(equips_dir)
                print(f"已清理英雄装备数据目录: {equips_dir}")
            except Exception as e:
                print(f"清理英雄装备数据目录时出错: {str(e)}")
        
        # 清理映射文件
        mapping_dir = os.path.join(base_dir, "映射文件")
        if os.path.exists(mapping_dir):
            # 清空英雄装备映射文件
            equip_mapping_file = os.path.join(mapping_dir, "英雄装备映射.json")
            if os.path.exists(equip_mapping_file):
                try:
                    with open(equip_mapping_file, 'w', encoding='utf-8') as f:
                        f.write(json.dumps({}, ensure_ascii=False, indent=2))
                    print(f"已清空映射文件: {equip_mapping_file}")
                except Exception as e:
                    print(f"清空映射文件时出错: {equip_mapping_file}, 错误: {str(e)}")
            
            # 删除多余的旧映射文件
            old_mapping_files = ['英雄名称映射.json', '英雄ID映射.json', '英雄图标映射.json']
            for old_file in old_mapping_files:
                old_file_path = os.path.join(mapping_dir, old_file)
                if os.path.exists(old_file_path):
                    try:
                        os.remove(old_file_path)
                        print(f"已删除多余的映射文件: {old_file}")
                    except Exception as e:
                        print(f"删除映射文件时出错: {old_file}, 错误: {str(e)}")
        
        self.status_var.set("数据清理完成")
        messagebox.showinfo("清理完成", "所有爬取的数据已清理完成！")
    
    def _delete_directory_contents(self, directory):
        """删除目录下的所有内容，但保留目录本身"""
        for root, dirs, files in os.walk(directory):
            for file in files:
                try:
                    os.remove(os.path.join(root, file))
                except Exception as e:
                    print(f"删除文件时出错: {os.path.join(root, file)}, 错误: {str(e)}")
            
            # 删除子目录（从内向外）
            for dir in dirs:
                try:
                    dir_path = os.path.join(root, dir)
                    # 仅当子目录为空时才删除
                    if not os.listdir(dir_path):
                        os.rmdir(dir_path)
                except Exception as e:
                    print(f"删除目录时出错: {os.path.join(root, dir)}, 错误: {str(e)}")
    
    def _start_scraping(self):
        """开始爬取数据"""
        # 检查至少选择了一项爬取内容
        if not (self.fetch_heroes_var.get() or self.fetch_icons_var.get() or self.fetch_equips_var.get()):
            messagebox.showwarning("警告", "请至少选择一项爬取内容！")
            return
            
        # 清空日志
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 更新UI状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.status_var.set("正在爬取...")
        
        # 获取参数
        base_dir = self.dir_var.get()
        max_concurrent = self.concurrency_var.get()
        timeout = self.timeout_var.get() if self.timeout_var.get() > 0 else None
        
        # 爬取选项
        fetch_heroes = self.fetch_heroes_var.get()
        fetch_icons = self.fetch_icons_var.get()
        fetch_equips = self.fetch_equips_var.get()
        
        # 随机延迟
        min_delay = self.min_delay_var.get()
        max_delay = self.max_delay_var.get()
        
        # 使用最新数据选项
        use_latest = self.use_latest_var.get()
        
        # 确保最小延迟不大于最大延迟
        if min_delay > max_delay:
            min_delay, max_delay = max_delay, min_delay
            self.min_delay_var.set(min_delay)
            self.max_delay_var.set(max_delay)
        
        # 在新线程中运行爬虫
        self.scrape_thread = threading.Thread(
            target=self._run_scraper,
            args=(
                base_dir, 
                max_concurrent, 
                timeout,
                fetch_heroes,
                fetch_icons,
                fetch_equips,
                min_delay,
                max_delay,
                use_latest  # 添加use_latest参数
            )
        )
        self.scrape_thread.daemon = True
        self.scrape_thread.start()
        
        # 启动进度更新定时器
        self.root.after(100, self._update_progress)
    
    def _run_scraper(self, base_dir, max_concurrent, timeout, 
                    fetch_heroes, fetch_icons, fetch_equips,
                    min_delay, max_delay, use_latest):
        """运行爬虫"""
        # 禁用开始按钮
        self.start_button.configure(state="disabled")
        # 启用停止按钮
        self.stop_button.configure(state="normal")
        
        # 清空日志框并显示启动信息
        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(tk.END, "开始爬取，请等待...\n")
        self.log_text.insert(tk.END, f"使用最新数据: {'是' if use_latest else '否'}\n")
        self.log_text.see(tk.END)
        
        # 启动爬虫线程
        self.scraper_running = True
        threading.Thread(
            target=self._run_scraper_thread,
            args=(base_dir, max_concurrent, timeout, 
                  fetch_heroes, fetch_icons, fetch_equips,
                  min_delay, max_delay, use_latest),  # 传递 use_latest 参数
            daemon=True
        ).start()
    
    def _run_scraper_thread(self, base_dir, max_concurrent, timeout, 
                           fetch_heroes, fetch_icons, fetch_equips,
                           min_delay, max_delay, use_latest):
        """爬虫线程"""
        try:
            global run_hero_equipment_scraper
            # 运行爬虫
            success = run_hero_equipment_scraper(
                max_concurrent=max_concurrent,
                base_dir=base_dir,
                skip_equips=not fetch_equips,
                timeout=timeout,
                download_icons=fetch_icons,
                fetch_heroes=fetch_heroes,
                gui=False,  # 不使用GUI模式，防止创建新窗口
                min_delay=min_delay,
                max_delay=max_delay,
                use_latest=use_latest  # 添加此参数
            )
            
            # 在主线程中更新UI
            self.root.after(0, self._scraping_finished, success)
        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, self._show_error, str(e))
    
    def _update_progress(self):
        """更新进度显示"""
        if hasattr(self, 'scraper') and self.scraper and hasattr(self.scraper, 'progress_info'):
            info = self.scraper.progress_info
            total = info.get("total", 0)
            current = info.get("current", 0)
            
            if total > 0:
                progress = (current / total) * 100
                self.progress_var.set(progress)
                
                status = info.get("status", "")
                message = info.get("message", "")
                self.status_var.set(f"{status} - {message} ({current}/{total})")
        
        # 如果爬取线程仍在运行，继续更新进度
        if self.scrape_thread and self.scrape_thread.is_alive():
            self.root.after(100, self._update_progress)
    
    def _scraping_finished(self, success):
        """爬取完成后的处理"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        if success:
            self.status_var.set("爬取完成")
            messagebox.showinfo("爬取完成", "英雄装备数据爬取成功！")
        else:
            self.status_var.set("爬取失败")
            messagebox.showerror("爬取失败", "英雄装备数据爬取时出错，请查看日志。")
    
    def _show_error(self, error_message):
        """显示错误信息"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("爬取出错")
        
        messagebox.showerror("爬取出错", f"爬取过程中出现错误：\n{error_message}")
    
    def _stop_scraping(self):
        """停止爬取"""
        if messagebox.askyesno("确认停止", "确定要停止当前爬取任务吗？"):
            print("用户手动停止爬取")
            self.status_var.set("正在停止...")
            
            # TODO: 实现爬取停止逻辑
            # 由于异步爬虫难以立即停止，这里可能需要一些额外处理
            
            # 恢复UI状态
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.status_var.set("已停止")
    
    def run(self):
        """运行GUI主循环"""
        # 恢复原始标准输出
        if hasattr(self, 'original_stdout'):
            sys.stdout = self.original_stdout
        
        # 启动事件循环
        self.root.mainloop()

    def _redirect_stdout(self):
        """将标准输出重定向到日志文本框"""
        class TextRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget
            
            def write(self, string):
                self.text_widget.config(state=tk.NORMAL)
                self.text_widget.insert(tk.END, string)
                self.text_widget.see(tk.END)
                self.text_widget.config(state=tk.DISABLED)
                self.text_widget.update_idletasks()
            
            def flush(self):
                pass
        
        # 保存原始标准输出
        self.original_stdout = sys.stdout
        
        # 重定向到文本框
        sys.stdout = TextRedirector(self.log_text)
    
    def _browse_dir(self):
        """浏览并选择保存目录"""
        dir_path = filedialog.askdirectory(initialdir=self.dir_var.get())
        if dir_path:
            self.dir_var.set(dir_path)

class HeroEquipmentScraper:
    """
    云顶之奕英雄装备数据爬取类 (Playwright版)
    负责从tactics.tools获取英雄中英文名称映射和装备数据
    """
    
    def __init__(self, base_dir=None, max_concurrent=3, min_delay=0.5, max_delay=1.0, use_latest=False):
        """
        初始化爬虫
        
        参数:
            base_dir: 基础数据保存目录
            max_concurrent: 最大并发数
            min_delay: 随机延迟最小值(秒)
            max_delay: 随机延迟最大值(秒)
            use_latest: 是否使用最新数据 (/latest 后缀)
        """
        # 判断是否使用最新数据
        self.use_latest = use_latest
        
        # 设置基础 URL (不带/latest后缀，仅在具体请求时添加)
        self.base_url = "https://tactics.tools/zh/units"
        
        # 设置基础目录(默认为脚本所在目录)
        self.base_dir = base_dir or SCRIPT_DIR
        
        # 创建数据保存目录结构
        self.data_structure = {
            'heroes_dir': os.path.join(self.base_dir, "英雄图标"),
            'equips_dir': os.path.join(self.base_dir, "英雄装备数据"),
            'mapping_dir': os.path.join(self.base_dir, "映射文件")
        }
        
        # 创建所有必要的目录
        for dir_path in self.data_structure.values():
            os.makedirs(dir_path, exist_ok=True)
        
        # 存储爬取的数据
        self.heroes_data = []  # 存储英雄基础数据
        self.hero_name_mapping = {}  # 存储英雄中英文名称映射
        
        # 并发设置
        self.max_concurrent = max_concurrent
        self.semaphore = None  # 将在async_init中初始化
        
        # 随机延迟设置
        self.min_delay = min_delay
        self.max_delay = max_delay
        
        # 选择器配置 (可以考虑移除 hero_icon 选择器，因为它不再被直接使用)
        self.selectors = {
            # 费用对应的英雄容器选择器
            'cost_containers': {
                1: "#content-container > div.flex.flex-col.w-full > div > div.css-1kzvjqq",
                2: "#content-container > div.flex.flex-col.w-full > div > div.css-1bcrhpw", # 添加了缺失的 2 费选择器
                3: "#content-container > div.flex.flex-col.w-full > div > div.css-6andx8",
                4: "#content-container > div.flex.flex-col.w-full > div > div.css-1mb4k10",
                5: "#content-container > div.flex.flex-col.w-full > div > div.css-1689428"
            },
            # 英雄子容器
            'hero_container': "div > a",
            # 英雄中文名
            'hero_cn_name': "div > div.flex.flex-col.overflow-hidden.h-\\[56px\\].p-2.py-\\[7px\\].lg\\:h-\\[60px\\].lg\\:pl-\\[10px\\].lg\\:pr-\\[3px\\] > div.text-base.font-medium.whitespace-no-wrap.truncate.css-l6s31b",
            # 英雄图标选择器 (此选择器在新逻辑下不再需要，但暂不移除以防万一)
            'hero_icon': "div > div.flex.flex-col.flex-shrink-0.justify-between.relative.h-\\[60px\\] > img",
            
            # 装备页面选择器
            'equip_tabs': {
                'single': "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.flex.justify-between.pt-2.max-w-\\[100vw\\] > div.MuiTabs-root.css-26p5om > div.MuiTabs-scroller.MuiTabs-hideScrollbar.MuiTabs-scrollableX.css-1t0s2fz > div > button:nth-child(1)",
                'double': "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.flex.justify-between.pt-2.max-w-\\[100vw\\] > div.MuiTabs-root.css-26p5om > div.MuiTabs-scroller.MuiTabs-hideScrollbar.MuiTabs-scrollableX.css-1t0s2fz > div > button:nth-child(2)",
                'triple': "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.flex.justify-between.pt-2.max-w-\\[100vw\\] > div.MuiTabs-root.css-26p5om > div.MuiTabs-scroller.MuiTabs-hideScrollbar.MuiTabs-scrollableX.css-1t0s2fz > div > button:nth-child(3)"
            },
            
            # 装备数据选择器
            'single_equip': {
                'container': "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.font-medium.font-montserrat.text-\\[15px\\].leading-\\[22px\\] > div:nth-child(3) > div > div.sticky.left-0.z-10.tbl-body-inner-md.pl-\\[4px\\].css-1xe62cb",
                'row': "div",
                'img': "div > div > a > img",
                'name': "div > div > a"
            },
            'multi_equip': {
                'container': "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.font-medium.font-montserrat.text-\\[15px\\].leading-\\[22px\\] > div:nth-child(3) > div > div.sticky.left-0.z-10.tbl-body-inner-md.pl-\\[4px\\].css-1djfgjf",
                'row': "div",
                'img1': "div > div > img:nth-child(1)",
                'img2': "div > div > img:nth-child(2)",
                'img3': "div > div > img:nth-child(3)"
            },
            
            # 装备统计数据选择器
            'stats': {
                'appearance_rate': "#tbl-body > div:nth-child(1) > div:nth-child(1)",
                'avg_rank': "#tbl-body > div:nth-child(1) > div.flex.items-center.justify-end.px-\\[14px\\].css-ko4j5d.tbl-cell-right-border",
                'top4_rate': "#tbl-body > div:nth-child(1) > div.flex.items-center.justify-end.px-\\[14px\\].css-1sdotvz.tbl-cell-right-border",
                'top1_rate': "#tbl-body > div:nth-child(1) > div.flex.items-center.justify-end.px-\\[14px\\].css-1m6c4m1"
            }
        }
        
        # 加载已存在的映射文件
        self._load_existing_mappings()
        
        # 记录已下载的英雄图标
        self.downloaded_icons = set()
        
        # 进度信息
        self.progress_info = {
            "status": "空闲",
            "total": 0,
            "current": 0,
            "message": ""
        }
        
        # 设置行高度(用于translateY解析)
        self.row_height = 46
    
    def _load_existing_mappings(self):
        """加载已存在的英雄装备映射文件"""
        # 加载英雄装备映射
        equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
        if os.path.exists(equip_mapping_file):
            try:
                with open(equip_mapping_file, 'r', encoding='utf-8') as f:
                    hero_equip_mapping = json.load(f)
                
                # 从装备映射提取英雄信息
                for hero_name, hero_info in hero_equip_mapping.items():
                    if "en_name" in hero_info and "cost" in hero_info:
                        # 提取到英雄名称映射中
                        self.hero_name_mapping[hero_name] = {
                            "en_name": hero_info.get("en_name"),
                            "cn_name": hero_name,
                            "cost": hero_info.get("cost"),
                            "icon_url": hero_info.get("icon_url")
                        }
                
                print(f"已加载英雄装备映射: {len(hero_equip_mapping)} 条记录")
            except Exception as e:
                print(f"加载英雄装备映射失败: {str(e)}")
    
    async def async_init(self):
        """异步初始化"""
        # 创建并发控制的信号量
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
    
    # 移除内置图标下载功能 - 现在统一使用高清图标下载.py

    async def add_random_delay(self):
        """添加随机延迟，防止被网站反爬"""
        if self.min_delay > 0 or self.max_delay > 0:
            delay = random.uniform(self.min_delay, self.max_delay)
            await asyncio.sleep(delay)
            
    async def clean_selected_data(self, clean_heroes=True, clean_icons=True, clean_equips=True):
        """
        根据选择清理特定的数据，保留英雄图标
        
        参数:
            clean_heroes: 是否清理英雄基础数据和映射
            clean_icons: 是否验证并清理英雄图标映射（但不删除实际图片）
            clean_equips: 是否清理英雄装备数据
        """
        print("根据选择清理数据...")
        
        # 清理装备数据
        if clean_equips:
            equips_dir = self.data_structure['equips_dir']
            if os.path.exists(equips_dir):
                await self._delete_directory_contents(equips_dir)
                print(f"已清理英雄装备数据目录: {equips_dir}")
        
        # 清理英雄基础数据和映射文件
        if clean_heroes or clean_equips:
            # 如果同时清理两种数据，则完全重置映射
            if clean_heroes and clean_equips:
                # 清空英雄装备映射
                equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
                if os.path.exists(equip_mapping_file):
                    with open(equip_mapping_file, 'w', encoding='utf-8') as f:
                        f.write(json.dumps({}, ensure_ascii=False, indent=2))
                    print(f"已清空英雄装备映射文件: {equip_mapping_file}")
            else:
                # 如果只清理一种，则更新映射中的相关数据
                equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
                if os.path.exists(equip_mapping_file):
                    try:
                        with open(equip_mapping_file, 'r', encoding='utf-8') as f:
                            equip_mapping = json.load(f)
                        
                        if clean_heroes:
                            # 保留装备数据路径，清除英雄基本信息
                            for hero_name in equip_mapping:
                                hero_info = equip_mapping[hero_name]
                                if "data_path" in hero_info:  # 保留装备数据路径
                                    data_path = hero_info["data_path"]
                                    equip_mapping[hero_name] = {"data_path": data_path}
                        
                        if clean_equips:
                            # 保留英雄基本信息，清除装备数据路径
                            for hero_name in equip_mapping:
                                if "data_path" in equip_mapping[hero_name]:
                                    del equip_mapping[hero_name]["data_path"]
                        
                        with open(equip_mapping_file, 'w', encoding='utf-8') as f:
                            f.write(json.dumps(equip_mapping, ensure_ascii=False, indent=2))
                        print(f"已更新英雄装备映射文件: {equip_mapping_file}")
                    except Exception as e:
                        print(f"更新英雄装备映射文件时出错: {str(e)}")
            
            # 清理旧的多余映射文件
            old_mapping_files = ['英雄名称映射.json', '英雄ID映射.json', '英雄图标映射.json']
            for old_file in old_mapping_files:
                old_file_path = os.path.join(self.data_structure['mapping_dir'], old_file)
                if os.path.exists(old_file_path):
                    try:
                        os.remove(old_file_path)
                        print(f"已删除多余的映射文件: {old_file}")
                    except Exception as e:
                        print(f"删除映射文件时出错: {old_file}, 错误: {str(e)}")
            
            # 清空已有数据
            if clean_heroes:
                self.heroes_data = []
                self.hero_name_mapping = {}
                self.downloaded_icons = set()
        
        print("数据清理完成")

    async def verify_missing_hero_icons(self):
        """
        验证英雄图标映射中的图片是否实际存在，只下载缺失的图片
        
        返回:
            list: 缺失图标的英雄数据列表
        """
        print("验证英雄图标完整性...")
        missing_icons = []
        
        # 加载英雄装备映射
        equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
        if not os.path.exists(equip_mapping_file):
            print("英雄装备映射文件不存在，无法验证图标")
            return missing_icons
        
        try:
            with open(equip_mapping_file, 'r', encoding='utf-8') as f:
                hero_equip_mapping = json.load(f)
            
            # 检查每个英雄的图标
            for hero_name, hero_info in hero_equip_mapping.items():
                if "img_path" in hero_info:
                    # 获取图标的绝对路径
                    relative_path = hero_info["img_path"]
                    absolute_path = os.path.join(self.base_dir, relative_path)
                    
                    # 检查文件是否存在
                    if not os.path.exists(absolute_path) or not os.path.isfile(absolute_path):
                        print(f"发现缺失的英雄图标: {hero_name}")
                        # 将英雄添加到缺失列表
                        missing_icons.append({
                            "cn_name": hero_name,
                            "en_name": hero_info.get("en_name"),
                            "cost": hero_info.get("cost"),
                            "icon_url": hero_info.get("icon_url")
                        })
                else:
                    # 如果映射中没有图标路径，也添加到缺失列表
                    if "en_name" in hero_info and "cost" in hero_info:
                        print(f"发现缺少图标路径的英雄: {hero_name}")
                        missing_icons.append({
                            "cn_name": hero_name,
                            "en_name": hero_info.get("en_name"),
                            "cost": hero_info.get("cost"),
                            "icon_url": hero_info.get("icon_url")
                        })
            
            print(f"验证完成，发现 {len(missing_icons)} 个缺失的英雄图标")
            return missing_icons
            
        except Exception as e:
            print(f"验证英雄图标时出错: {str(e)}")
            return missing_icons

    async def _delete_directory_contents(self, directory):
        """删除目录下的所有内容，但保留目录本身"""
        for root, dirs, files in os.walk(directory, topdown=False):
            for file in files:
                try:
                    os.remove(os.path.join(root, file))
                except Exception as e:
                    print(f"删除文件时出错: {os.path.join(root, file)}, 错误: {str(e)}")
            
            # 删除子目录
            for dir_name in dirs:
                try:
                    dir_path = os.path.join(root, dir_name)
                    # 递归删除子目录内容
                    await self._delete_directory_contents(dir_path)
                    # 尝试删除空目录
                    if os.path.exists(dir_path) and not os.listdir(dir_path):
                        os.rmdir(dir_path)
                except Exception as e:
                    print(f"删除目录时出错: {os.path.join(root, dir_name)}, 错误: {str(e)}")
    
    async def run(self, skip_equips=False, fetch_heroes=True):
        """
        运行爬虫

        参数:
            skip_equips: 是否跳过装备数据爬取，只爬取英雄列表
            fetch_heroes: 是否爬取英雄列表和基础信息

        注意: 图标下载功能已移除，现在统一使用高清图标下载.py
        """
        async with async_playwright() as playwright:
            # 初始化
            await self.async_init()
            
            # 爬取英雄列表
            if fetch_heroes:
                print("第1阶段：爬取英雄列表数据...")
                
                heroes_list = await self.get_heroes_list(playwright)
                
                if not heroes_list:
                    print("错误：无法获取英雄列表数据，程序终止")
                    return False
                
                print(f"成功爬取 {len(heroes_list)} 个英雄的基本信息")
            else:
                print("跳过爬取英雄列表数据...")
                # 加载已有的英雄列表数据
                equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
                if os.path.exists(equip_mapping_file):
                    try:
                        with open(equip_mapping_file, 'r', encoding='utf-8') as f:
                            hero_equip_mapping = json.load(f)
                        
                        # 从映射构建英雄列表
                        self.heroes_data = []
                        for cn_name, hero_info in hero_equip_mapping.items():
                            if "en_name" in hero_info:
                                self.heroes_data.append({
                                    "en_name": hero_info.get("en_name"),
                                    "cn_name": cn_name,
                                    "cost": hero_info.get("cost"),
                                    "icon_url": hero_info.get("icon_url"),
                                    "detail_url": f"{self.base_url}/{hero_info.get('en_name')}" + ("/latest" if self.use_latest else ""),
                                    "img_path": hero_info.get("img_path") # 添加相对路径
                                })
                                
                                # 更新英雄名称映射
                                self.hero_name_mapping[cn_name] = {
                                    "en_name": hero_info.get("en_name"),
                                    "cn_name": cn_name,
                                    "cost": hero_info.get("cost"),
                                    "icon_url": hero_info.get("icon_url"),
                                    "img_path": hero_info.get("img_path") # 添加相对路径
                                }
                                
                                # 如果有图标路径，也添加到映射中
                                if "img_path" in hero_info:
                                    self.hero_name_mapping[cn_name]["img_path"] = hero_info["img_path"]
                        
                        print(f"已从映射加载 {len(self.heroes_data)} 个英雄的基本信息")
                        heroes_list = self.heroes_data
                    except Exception as e:
                        print(f"加载英雄装备映射失败: {str(e)}")
                        # 如果无法加载，则需要爬取
                        heroes_list = await self.get_heroes_list(playwright)
                        
                        if not heroes_list:
                            print("错误：无法获取英雄列表数据，程序终止")
                            return False
                        
                        print(f"成功爬取 {len(heroes_list)} 个英雄的基本信息")
                else:
                    print("未找到英雄装备映射文件，需要爬取英雄列表数据...")
                    heroes_list = await self.get_heroes_list(playwright)
                    
                    if not heroes_list:
                        print("错误：无法获取英雄列表数据，程序终止")
                        return False
                    
                    print(f"成功爬取 {len(heroes_list)} 个英雄的基本信息")
            
            # 如果跳过装备数据爬取，则直接返回
            if skip_equips:
                return True
            
            # 爬取英雄装备数据
            await self.scrape_hero_equipment(playwright, heroes_list)
            
            return True

    async def _construct_hero_icon_url(self, en_name: str):
        """
        根据英雄英文名构造图标 URL。

        参数:
            en_name: 英雄的英文名 (通常是小写)

        返回:
            构造好的图标 URL，如果 en_name 为空则返回 None
        """
        if not en_name:
            return None
        try:
            # 将英文名首字母大写以匹配 URL 格式 (例如 'alistar' -> 'Alistar')
            hero_name_capitalized = en_name.capitalize()
            # 使用 f-string 根据模板构造 URL
            # 注意：这里的 'cd14' 和 'TFT14_' 是基于你提供的模板硬编码的，
            # 如果游戏版本/赛季更新，这部分可能需要动态获取或修改。
            icon_url = f"https://ap.tft.tools/img/dc15/face/tft15_{hero_name_capitalized}.jpg?w=56"
            return icon_url
        except Exception as e:
            print(f"构造英雄 {en_name} 图标 URL 时出错: {str(e)}")
            return None

    # 移除英雄图标下载相关方法 - 现在统一使用高清图标下载.py
        # hero_equip_mapping_file = os.path.join(self.base_dir, "映射文件", '英雄装备映射.json')
        # hero_equip_mapping = {}
        # 
        # 如果映射文件存在，先读取现有映射
        # if os.path.exists(hero_equip_mapping_file):
        #     try:
        #         with open(hero_equip_mapping_file, 'r', encoding='utf-8') as f:
        #             hero_equip_mapping = json.load(f)
        #     except:
        #         hero_equip_mapping = {}
        # 
        # 如果英雄已存在于映射中，则更新图标路径
        # if hero_name in hero_equip_mapping:
        #     hero_equip_mapping[hero_name]["img_path"] = icon_path
        # else:
        #     # 如果英雄不存在，先创建基本条目
        #     for hero in self.heroes_data:
        #         if hero.get("cn_name") == hero_name:
        #             hero_equip_mapping[hero_name] = {
        #                 "en_name": hero.get("en_name"),
        #                 "cn_name": hero_name,
        #                 "cost": hero.get("cost"),
        #                 "img_path": icon_path
        #             }
        #             break
        # 
        # 保存映射
        # with open(hero_equip_mapping_file, 'w', encoding='utf-8') as f:
        #     f.write(json.dumps(hero_equip_mapping, ensure_ascii=False, indent=2))
            
    async def save_data_to_json(self):
        """将爬取的数据保存为JSON文件，主要保存英雄装备映射"""
        if not self.heroes_data:
            print("没有数据可保存")
            return False
        
        try:
            # 更新英雄装备映射
            hero_equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
            hero_equip_mapping = {}
            
            # 读取现有映射
            if os.path.exists(hero_equip_mapping_file):
                try:
                    with open(hero_equip_mapping_file, 'r', encoding='utf-8') as f:
                        hero_equip_mapping = json.load(f)
                except Exception as e:
                    print(f"读取英雄装备映射失败，将创建新文件: {str(e)}")
            
            # 更新/添加英雄信息
            for hero in self.heroes_data:
                cn_name = hero.get("cn_name")
                if not cn_name:
                    continue
                    
                # 确保所有必要信息都存在
                en_name = hero.get("en_name")
                cost = hero.get("cost")
                icon_url = hero.get("icon_url")
                img_path = hero.get("img_path") # 从 hero_data 获取已计算好的路径
                
                if cn_name in hero_equip_mapping:
                    # 保留已有数据但更新基本信息
                    hero_equip_mapping[cn_name].update({
                        "en_name": en_name,
                        "cost": cost,
                        "icon_url": icon_url,
                        "img_path": img_path # 确保 img_path 也被更新
                    })
                else:
                    # 创建新条目
                    hero_equip_mapping[cn_name] = {
                        "en_name": en_name,
                        "cn_name": cn_name,
                        "cost": cost,
                        "icon_url": icon_url,
                        "img_path": img_path # 创建新条目时也包含 img_path
                    }
            
            # 保存更新后的映射
            with open(hero_equip_mapping_file, 'w', encoding='utf-8') as f:
                f.write(json.dumps(hero_equip_mapping, ensure_ascii=False, indent=2))
            print(f"英雄装备映射已更新: {hero_equip_mapping_file}")
            
            # 删除多余的旧映射文件
            old_mapping_files = ['英雄名称映射.json', '英雄ID映射.json', '英雄图标映射.json']
            for old_file in old_mapping_files:
                old_file_path = os.path.join(self.data_structure['mapping_dir'], old_file)
                if os.path.exists(old_file_path):
                    try:
                        os.remove(old_file_path)
                        print(f"已删除多余的映射文件: {old_file}")
                    except Exception as e:
                        print(f"删除映射文件时出错: {old_file}, 错误: {str(e)}")
            
            return True
        except Exception as e:
            print(f"保存数据时出错: {str(e)}")
            return False

    async def scrape_hero_equipment(self, playwright, heroes_list):
        """
        爬取所有英雄的装备数据，增加了单个英雄爬取的重试机制。

        参数:
            playwright: Playwright实例
            heroes_list: 英雄列表
        """
        print("\n第2阶段：爬取英雄装备关联数据...")

        if not heroes_list:
            print("没有英雄数据，无法爬取装备关联")
            return False

        print(f"开始爬取 {len(heroes_list)} 个英雄的装备关联数据，最大并发数：{self.max_concurrent}")

        start_time = time.time()
        total_count = len(heroes_list)

        # --- 修改：使用包装函数进行重试 ---
        async def _scrape_single_hero_with_retries(hero, max_retries=1):
            """包装函数，包含单个英雄爬取的重试逻辑"""
            en_name = hero.get("en_name")
            cn_name = hero.get("cn_name")
            if not en_name or not cn_name:
                return False # 无效英雄数据

            for attempt in range(max_retries + 1):
                try:
                    # 调用原始的单个英雄爬取函数
                    success = await self._scrape_single_hero_equipment(
                        playwright,
                        en_name,
                        cn_name
                    )
                    if success:
                        return True # 成功则返回
                    else:
                        # 如果 _scrape_single_hero_equipment 内部处理了错误并返回 False
                        print(f"爬取英雄 {cn_name} 失败 (尝试 {attempt+1}/{max_retries+1})，准备重试...")
                        # 这里可以根据返回 False 的原因决定是否重试，但目前统一重试
                except Exception as e:
                    # 捕获 _scrape_single_hero_equipment 抛出的未处理异常
                    print(f"=== 重试机制捕获异常 ===")
                    print(f"英雄中文名: {cn_name}")
                    print(f"英雄英文名: {en_name}")
                    print(f"尝试次数: {attempt+1}/{max_retries+1}")
                    print(f"异常类型: {type(e).__name__}")
                    print(f"异常信息: {str(e)}")
                    print(f"========================")
                    # 对于特定类型的异常（如 playwright.Error），可以决定是否重试

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries:
                    wait_time = 3 * (attempt + 1) # 线性增加等待时间 (3, 6, 9, ...)
                    print(f"将在 {wait_time} 秒后重试爬取英雄: {cn_name}")
                    await asyncio.sleep(wait_time)

            # 所有尝试失败后
            print(f"爬取英雄 {cn_name} 失败，已达到最大重试次数 ({max_retries})")
            return False
        # --- 修改结束 ---


        # 创建英雄分批次，每批最多10个 (保持不变)
        batch_size = 10
        hero_batches = [heroes_list[i:i+batch_size] for i in range(0, total_count, batch_size)]

        total_success = 0
        batch_num = 1

        for batch in hero_batches:
            batch_start_time = time.time()
            print(f"\n批次 {batch_num}/{len(hero_batches)}，处理 {len(batch)} 个英雄...")

            try:
                tasks = []
                for hero in batch:
                    # --- 修改：创建重试任务 ---
                    task = _scrape_single_hero_with_retries(hero, max_retries=1) # 设置每个英雄最多重试1次
                    tasks.append(task)
                    # --- 修改结束 ---

                # 并发执行所有任务 (保持不变)
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 统计成功数 (需要处理 gather 可能返回的异常)
                current_batch_success = 0
                for i, r in enumerate(results):
                    if r is True:
                        current_batch_success += 1
                    elif isinstance(r, Exception):
                        # 如果 gather 捕获了未处理的异常
                        hero_name = batch[i].get('cn_name', '未知英雄')
                        print(f"处理英雄 {hero_name} 时遇到未捕获异常: {r}")
                        # traceback.print_exc() # 可以取消注释以打印详细堆栈

                total_success += current_batch_success

                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time

                print(f"批次 {batch_num} 完成：成功 {current_batch_success}/{len(batch)}，耗时 {batch_duration:.2f} 秒")

                # 如果成功率低于50%，增加下一批次的间隔时间 (保持不变)
                if len(batch) > 0 and current_batch_success / len(batch) < 0.5 and batch_num < len(hero_batches):
                    pause_time = 10
                    print(f"成功率低于50%，暂停 {pause_time} 秒后继续...")
                    await asyncio.sleep(pause_time)

            except Exception as e:
                print(f"批次 {batch_num} 处理出错: {str(e)}")

            batch_num += 1

        end_time = time.time()
        total_duration = end_time - start_time

        print(f"\n英雄装备数据爬取完成：成功 {total_success}/{total_count}，总耗时 {total_duration:.2f} 秒")

        # 返回值可以根据总成功数判断，或者只要有部分成功就算成功
        return total_success > 0 # 或者 return total_success == total_count

    async def _scrape_single_hero_equipment(self, playwright, en_name, cn_name):
        """
        爬取单个英雄的装备数据
        
        参数:
            playwright: Playwright实例
            en_name: 英雄英文名
            cn_name: 英雄中文名
        """
        browser = None
        context = None
        page = None
        try:
            async with self.semaphore:
                # 构建英雄详情页 URL，根据 use_latest 参数判断是否添加 /latest
                base_url = "https://tactics.tools/zh/units"
                if self.use_latest:
                    detail_url = f"{base_url}/{en_name}/latest"
                else:
                    detail_url = f"{base_url}/{en_name}"
                
                browser = await playwright.chromium.launch(headless=True)

                # 创建上下文和页面
                context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                )

                page = await context.new_page()
                page.set_default_timeout(60000)  # 60秒超时

                # 打开英雄详情页
                print(f"打开英雄 {cn_name} 详情页: {detail_url}")
                # --- 修改: 增加对 TargetClosedError 的捕获, 调整goto参数 ---
                try:
                    print(f"导航到 {detail_url}，等待 'domcontentloaded'，超时120秒...") # 增加日志，指明等待策略和超时
                    await page.goto(detail_url, wait_until="domcontentloaded", timeout=120000) # 增加超时时间至120秒, 将等待条件改为 domcontentloaded
                    print(f"页面 {detail_url} DOMContentLoaded 完成。") # 增加日志
                    
                    # 等待英雄基础统计数据的容器加载完成
                    # 这个容器包含了英雄的出场率、平均排名等核心数据
                    hero_stats_container_selector = ("#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > "
                                                     "div.flex.flex-col.gap-4 > "
                                                     "div.rounded.text-white1.text-sm.sm\\:text-base.bg-bg")
                    print(f"等待英雄基础数据容器加载 (超时30秒): {hero_stats_container_selector}")
                    await page.wait_for_selector(hero_stats_container_selector, timeout=30000) # 等待特定元素出现，超时30秒
                    print(f"英雄基础数据容器已成功加载。")

                except Exception as e:
                    # 统一处理 page.goto 和 page.wait_for_selector 可能抛出的异常
                    error_source = "page.goto" if "goto" in str(e).lower() or "navigation" in str(e).lower() else "page.wait_for_selector"
                    error_description = f"在 {error_source} 期间发生错误 ({cn_name} - {detail_url})"

                    # 强制打印错误信息，确保能看到具体的英雄信息
                    print(f"=== 错误详情 ===")
                    print(f"英雄中文名: {cn_name}")
                    print(f"英雄英文名: {en_name}")
                    print(f"访问URL: {detail_url}")
                    print(f"错误类型: {type(e).__name__}")
                    print(f"错误信息: {str(e)}")
                    print(f"===============")

                    if "Target page, context or browser has been closed" in str(e):
                        print(f"{error_description}: 目标已关闭。可能是任务被取消。")
                        return False # 目标关闭，无法继续
                    elif "Timeout" in str(e) or "Timeout" in type(e).__name__: # 更通用的超时检测
                        print(f"{error_description}: 操作超时。详细信息: {type(e).__name__} - {str(e)}")
                        raise # 重新抛出，由外部重试逻辑处理
                    else:
                        # 其他导航错误，记录并向上抛出，由重试机制处理
                        print(f"{error_description}: 未预期的错误。详细信息: {type(e).__name__} - {str(e)}")
                        raise # 重新抛出其他未处理的错误

                # 原有的 asyncio.sleep(5) 或 page.wait_for_timeout(5000) 逻辑已被替代
                # await asyncio.sleep(5) # 这行可以移除了

                # 创建英雄基础数据结构，包含费用信息
                hero_cost = None
                for hero in self.heroes_data:
                    if hero.get("en_name") == en_name:
                        hero_cost = hero.get("cost")
                        break

                # 初始化英雄数据结构
                hero_equip_data = {
                    "en_name": en_name,
                    "cn_name": cn_name,
                    "cost": hero_cost,
                    "single_equips": [],
                    "double_equips": [],
                    "triple_equips": []
                }

                # 获取英雄基础数据（出场率、平均排名、前四率、登顶率）
                try:
                    print(f"正在提取英雄 {cn_name} 的基础数据...")

                    # --- 保留原有基础数据提取逻辑 ---
                    # 出场率
                    playrate_selector = "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.rounded.text-white1.text-sm.sm\\:text-base.bg-bg > div.flex.gap-8.md\\:flex-row.flex-wrap.md\\:gap-12.p-4 > div:nth-child(1) > div.grid.gap-2.font-montserrat.font-medium.text-sm.leading-none.grid-cols-\\[minmax\\(auto\\,85px\\)\\,auto\\].grid-rows-\\[repeat\\(4\\,14px\\)\\] > div:nth-child(4)"
                    playrate_element = await page.query_selector(playrate_selector)
                    if playrate_element:
                        playrate = await playrate_element.text_content()
                        hero_equip_data["play_rate"] = playrate.strip()

                    # 平均排名
                    avgplace_selector = "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.rounded.text-white1.text-sm.sm\\:text-base.bg-bg > div.flex.gap-8.md\\:flex-row.flex-wrap.md\\:gap-12.p-4 > div:nth-child(1) > div.grid.gap-2.font-montserrat.font-medium.text-sm.leading-none.grid-cols-\\[minmax\\(auto\\,85px\\)\\,auto\\].grid-rows-\\[repeat\\(4\\,14px\\)\\] > div:nth-child(6)"
                    avgplace_element = await page.query_selector(avgplace_selector)
                    if avgplace_element:
                        avgplace = await avgplace_element.text_content()
                        hero_equip_data["avg_place"] = avgplace.strip()

                    # 前四率
                    top4_selector = "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.rounded.text-white1.text-sm.sm\\:text-base.bg-bg > div.flex.gap-8.md\\:flex-row.flex-wrap.md\\:gap-12.p-4 > div:nth-child(1) > div.grid.gap-2.font-montserrat.font-medium.text-sm.leading-none.grid-cols-\\[minmax\\(auto\\,85px\\)\\,auto\\].grid-rows-\\[repeat\\(4\\,14px\\)\\] > div:nth-child(8)"
                    top4_element = await page.query_selector(top4_selector)
                    if top4_element:
                        top4 = await top4_element.text_content()
                        hero_equip_data["top4_rate"] = top4.strip()

                    # 登顶率
                    top1_selector = "#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.rounded.text-white1.text-sm.sm\\:text-base.bg-bg > div.flex.gap-8.md\\:flex-row.flex-wrap.md\\:gap-12.p-4 > div:nth-child(1) > div.grid.gap-2.font-montserrat.font-medium.text-sm.leading-none.grid-cols-\\[minmax\\(auto\\,85px\\)\\,auto\\].grid-rows-\\[repeat\\(4\\,14px\\)\\] > div:nth-child(10)"
                    top1_element = await page.query_selector(top1_selector)
                    if top1_element:
                        top1 = await top1_element.text_content()
                        hero_equip_data["top1_rate"] = top1.strip()

                    print(f"成功提取英雄 {cn_name} 的基础数据")

                except Exception as e:
                    # --- 修改: 增加对 TargetClosedError 的检查 ---
                    if "Target page, context or browser has been closed" in str(e):
                         print(f"错误：在提取 {cn_name} 基础数据时目标已关闭。")
                         return False # 无法继续提取，返回失败
                    else:
                        print(f"提取英雄 {cn_name} 的基础数据时出错: {str(e)}")


                # --- 爬取装备数据 ---
                # 使用translateY策略提取装备数据
                # 单装备
                # --- 修改: 增加对 TargetClosedError 的检查 ---
                try:
                    await self.extract_equips_by_translatey(page, 1, "single_equips", 1, hero_equip_data)
                except Exception as e:
                    if "Target page, context or browser has been closed" in str(e):
                        print(f"错误：在提取 {cn_name} 单装备数据时目标已关闭。")
                        return False
                    else:
                        print(f"提取英雄 {cn_name} 单装备数据时发生意外错误: {str(e)}")
                        # 可以选择继续提取其他数据或直接返回失败
                        # return False

                # 双装备
                # --- 修改: 增加对 TargetClosedError 的检查 ---
                try:
                    await self.extract_equips_by_translatey(page, 2, "double_equips", 2, hero_equip_data)
                except Exception as e:
                    if "Target page, context or browser has been closed" in str(e):
                        print(f"错误：在提取 {cn_name} 双装备数据时目标已关闭。")
                        return False
                    else:
                        print(f"提取英雄 {cn_name} 双装备数据时发生意外错误: {str(e)}")
                        # return False

                # 三装备
                # --- 修改: 增加对 TargetClosedError 的检查 ---
                try:
                    await self.extract_equips_by_translatey(page, 3, "triple_equips", 3, hero_equip_data)
                except Exception as e:
                    if "Target page, context or browser has been closed" in str(e):
                        print(f"错误：在提取 {cn_name} 三装备数据时目标已关闭。")
                        return False
                    else:
                        print(f"提取英雄 {cn_name} 三装备数据时发生意外错误: {str(e)}")
                        # return False

                # 保存英雄装备数据
                await self._save_hero_equipment_data(hero_equip_data)

                print(f"英雄 {cn_name} 的装备数据爬取完成: 单件装备 {len(hero_equip_data['single_equips'])}，双装备 {len(hero_equip_data['double_equips'])}，三装备 {len(hero_equip_data['triple_equips'])}")
                return True

        except Exception as e:
             # --- 修改: 增加对 TargetClosedError 的检查 ---
             if "Target page, context or browser has been closed" not in str(e):
                 # 只打印非 TargetClosedError 的异常，因为 TargetClosedError 在 finally 中处理或在上面已经处理
                 print(f"爬取英雄 {cn_name} 的装备数据时出错: {str(e)}")
                 traceback.print_exc() # 打印堆栈跟踪获取更多错误信息
             # else: # 如果是 TargetClosedError，可能已经在上面处理或将在 finally 中静默处理
             #    print(f"爬取英雄 {cn_name} 时发生 TargetClosedError，可能是任务被取消。")
             return False

        finally:
            # --- 修改: 增强 finally 块的健壮性 ---
            # 确保所有资源都被尝试关闭，即使发生错误也继续关闭其他资源
            # 并捕获关闭过程中可能出现的 TargetClosedError
            print(f"开始清理英雄 {cn_name} 的 Playwright 资源...")
            close_errors = [] # 记录关闭时发生的错误

            if page:
                try:
                    # 检查页面是否已关闭
                    if not page.is_closed():
                        await page.close()
                        print(f"页面资源 ({cn_name}) 已关闭。")
                    # else:
                    #     print(f"页面资源 ({cn_name}) 已是关闭状态。")
                except Exception as e:
                    # 捕获关闭页面时可能发生的错误，特别是 TargetClosedError
                    error_msg = f"关闭页面出错 ({cn_name}): {str(e)}"
                    # print(error_msg) # 可以选择不打印这个错误，因为它通常是预期的副作用
                    close_errors.append(error_msg)

            if context:
                try:
                    # Playwright context 没有 is_closed() 方法，直接尝试关闭
                    await context.close()
                    print(f"上下文资源 ({cn_name}) 已关闭。")
                except Exception as e:
                    # 捕获关闭上下文时可能发生的错误
                    error_msg = f"关闭上下文出错 ({cn_name}): {str(e)}"
                    # print(error_msg)
                    close_errors.append(error_msg)

            if browser:
                try:
                    # 检查浏览器是否已连接
                    if browser.is_connected():
                         await browser.close()
                         print(f"浏览器资源 ({cn_name}) 已关闭。")
                    # else:
                    #     print(f"浏览器资源 ({cn_name}) 已断开连接或关闭。")
                except Exception as e:
                    # 捕获关闭浏览器时可能发生的错误
                    error_msg = f"关闭浏览器出错 ({cn_name}): {str(e)}"
                    # print(error_msg)
                    close_errors.append(error_msg)

            # if close_errors:
                # 可以选择性地打印收集到的所有关闭错误
                # print(f"清理英雄 {cn_name} 资源时遇到以下问题:\n" + "\n".join(close_errors))
            # else:
            #     print(f"英雄 {cn_name} 的 Playwright 资源清理完成。")
            print(f"英雄 {cn_name} 的 Playwright 资源清理完成。")

    async def extract_equips_by_translatey(self, page, tab_index: int, data_key: str, expected_items: int, hero_equip_data):
        """使用 translateY 策略提取装备数据"""
        print(f"\n--- 开始提取 {data_key} 数据 ---")
        # 切换到对应标签
        if not await self._switch_to_tab(page, tab_index):
            print(f"无法切换到标签 {tab_index}，跳过 {data_key}")
            return

        # 1. 获取所有左侧静态行及其顺序索引
        static_rows_info = await self._get_static_rows_with_names(page, expected_items)
        if not static_rows_info:
            print(f"未能获取到 {data_key} 的静态行信息。")
            return
        print(f"找到 {len(static_rows_info)} 个 {data_key} 静态行。")

        # 2. 滚动页面并捕获所有动态数据及其 translateY
        dynamic_data_map = await self._scroll_and_capture_dynamic_data(page)
        if not dynamic_data_map:
            print(f"未能捕获到 {data_key} 的动态数据。")
            # 即使没捕获到动态数据，也继续尝试匹配，可能初始视口内有数据

        print(f"捕获了 {len(dynamic_data_map)} 条独特的动态行数据。")

        # 3. 匹配静态行和动态数据
        final_results = []
        match_count = 0
        missing_translate_keys = []

        for static_info in static_rows_info:
            logical_index = static_info["index"]
            equip_names = static_info["names"]
            expected_translateY = logical_index * self.row_height
            stats_data = dynamic_data_map.get(expected_translateY)

            if stats_data:
                match_count += 1
                entry = {}
                if expected_items == 1:
                    entry["equip_name"] = equip_names[0]
                else:
                    entry["equip_names"] = equip_names
                entry.update(stats_data)
                final_results.append(entry)
            else:
                missing_translate_keys.append(expected_translateY)

        print(f"匹配完成: {match_count} / {len(static_rows_info)} 条数据成功关联。")
        if len(static_rows_info) > match_count:
             print(f"  警告: 有 {len(static_rows_info) - match_count} 条静态行未能匹配到动态数据。")
             # print(f"  缺失的预期 translateY 值: {sorted(list(set(missing_translate_keys)))}")

        hero_equip_data[data_key] = final_results
        print(f"--- 成功存储 {len(hero_equip_data[data_key])} 条 {data_key} 数据 ---")

    async def _get_static_rows_with_names(self, page, expected_items: int):
        """获取左侧静态行，提取装备名，并附加逻辑索引"""
        static_rows_data = []
        static_row_elements = await page.query_selector_all(
            'div.sticky.left-0 div.tbl-row-md'
        )

        if not static_row_elements:
             print("错误：未能找到任何左侧静态行元素。请检查选择器。")
             return []

        print(f"初步找到 {len(static_row_elements)} 个左侧元素，开始过滤和提取名称...")

        valid_row_index = 0
        for i, row_element in enumerate(static_row_elements):
            equip_names = await self._get_equip_names_from_static_row(row_element, expected_items)
            if equip_names:
                static_rows_data.append({
                    "index": valid_row_index,
                    "names": equip_names
                })
                valid_row_index += 1

        return static_rows_data

    async def _get_equip_names_from_static_row(self, static_row_element, expected_items: int):
        """从单个左侧静态行元素中提取装备名称"""
        try:
            imgs = await static_row_element.query_selector_all('img')
            if not imgs: return None
            equip_names = []
            for img in imgs:
                name = await img.get_attribute('alt') or await img.get_attribute('title')
                if name and name.strip():
                    equip_names.append(name.strip())
            if len(equip_names) >= expected_items:
                return equip_names[:expected_items]
            else:
                return None
        except Exception:
            return None

    async def _scroll_and_capture_dynamic_data(self, page):
        """滚动页面，捕获所有出现的动态行数据及其 translateY"""
        print("开始滚动页面并捕获动态数据...")
        dynamic_data_map = {}
        scroll_element_handle = page
        self.row_height = 46  # 每个逻辑行的高度/translateY 增量
        self.scroll_pause_time = 0.1 # 每次滚动后的短暂暂停时间 (秒)
        self.scroll_step = 500 # 每次滚动多少像素
        self.max_scroll_attempts = 50 # 最大滚动次数，确保覆盖所有内容

        print("  捕获初始视口数据...")
        await self._capture_visible_dynamic_rows(page, dynamic_data_map)
        print(f"  初始捕获 {len(dynamic_data_map)} 条数据。")

        last_captured_count = 0
        no_new_data_streak = 0
        max_streak = 5

        for i in range(self.max_scroll_attempts):
            await scroll_element_handle.evaluate(f'window.scrollBy(0, {self.scroll_step})')
            await page.wait_for_timeout(self.scroll_pause_time * 1000)

            current_count_before = len(dynamic_data_map)
            await self._capture_visible_dynamic_rows(page, dynamic_data_map)
            current_count_after = len(dynamic_data_map)

            new_items_this_scroll = current_count_after - current_count_before
            print(f"  滚动 {i+1}/{self.max_scroll_attempts}: 捕获到 {new_items_this_scroll} 条新数据。总计: {current_count_after}")

            if new_items_this_scroll == 0:
                no_new_data_streak += 1
            else:
                no_new_data_streak = 0

            if no_new_data_streak >= max_streak:
                print(f"  连续 {max_streak} 次滚动未发现新数据，停止滚动。")
                break

        await self._capture_visible_dynamic_rows(page, dynamic_data_map)
        print(f"滚动结束。最终捕获 {len(dynamic_data_map)} 条独特的动态数据。")

        await page.evaluate('window.scrollTo(0, 0)')
        await page.wait_for_timeout(500)

        return dynamic_data_map

    async def _capture_visible_dynamic_rows(self, page, dynamic_data_map):
        """捕获当前 DOM 中所有 #tbl-body > div 的数据"""
        dynamic_rows = await page.query_selector_all("#tbl-body > div.flex.tbl-row-md")
        if not dynamic_rows:
             dynamic_rows = await page.query_selector_all("#tbl-body > div")

        for row in dynamic_rows:
            try:
                transform_style = await row.get_attribute("style")
                translateY = self._parse_translateY(transform_style)
                if translateY is not None and translateY not in dynamic_data_map:
                    stats = await self._extract_stats_from_dynamic_row(row)
                    if stats:
                        dynamic_data_map[translateY] = stats
            except Exception:
                pass

    def _parse_translateY(self, style_string):
        """从 style 字符串中解析 translateY 值"""
        if not style_string: return None
        match = re.search(r'translateY\(\s*(-?\d+(\.\d+)?)\s*px\s*\)', style_string)
        if match:
            try: return int(float(match.group(1)))
            except ValueError: return None
        match_top = re.search(r'top:\s*(-?\d+(\.\d+)?)\s*px', style_string)
        if match_top:
             try: return int(float(match_top.group(1)))
             except ValueError: return None
        return None

    async def _extract_stats_from_dynamic_row(self, dynamic_row_element):
        """从单个右侧动态行元素中提取统计数据"""
        try:
            cells = await dynamic_row_element.query_selector_all('div.flex.items-center.justify-end')
            if not cells:
                 cells = await dynamic_row_element.query_selector_all(':scope > div')

            # 数据提取已修复，移除调试代码

            if len(cells) >= 5:
                # 根据用户提供的正确列顺序修正数据提取
                # 列顺序：↑Play rate, Adj. Place, 排名, 前四%, 登顶%
                play_rate = (await cells[0].text_content() or "").strip()
                avg_place = (await cells[1].text_content() or "").strip()
                # cells[2] 是 "排名" 列，暂时跳过
                top4_rate = (await cells[3].text_content() or "").strip()
                top1_rate = (await cells[4].text_content() or "").strip()

                if play_rate and avg_place and top4_rate and top1_rate:
                    return {
                        "Play rate": play_rate,
                        "Avg place": avg_place,
                        "Top 4%": top4_rate,
                        "Top 1%": top1_rate,
                    }
                else: return None
            elif len(cells) >= 4:
                # 兼容旧版本，如果只有4列
                play_rate = (await cells[0].text_content() or "").strip()
                avg_place = (await cells[1].text_content() or "").strip()
                top4_rate = (await cells[2].text_content() or "").strip()
                top1_rate = (await cells[3].text_content() or "").strip()

                if play_rate and avg_place and top4_rate and top1_rate:
                    return {
                        "Play rate": play_rate,
                        "Avg place": avg_place,
                        "Top 4%": top4_rate,
                        "Top 1%": top1_rate,
                    }
                else: return None
            else: return None
        except Exception:
            return None

    async def _switch_to_tab(self, page, tab_index):
        """切换到指定的装备标签页"""
        print(f"尝试切换到标签页: {tab_index}")
        try:
            # 主选择器
            tab_selector = f'#content-container > div.flex-1.sm\\:w-\\[576px\\].md\\:w-\\[842px\\] > div.flex.flex-col.gap-4 > div.p-4.rounded.text-white1.px-0.py-0.bg-bg > div.flex.justify-between.pt-2.max-w-\\[100vw\\] > div.MuiTabs-root.css-26p5om > div.MuiTabs-scroller.MuiTabs-hideScrollbar.MuiTabs-scrollableX.css-1t0s2fz > div > button:nth-child({tab_index})'

            # 备用选择器
            backup_selector = f'.MuiTabs-root button:nth-child({tab_index})'

            # 尝试点击主选择器
            tab_element = await page.query_selector(tab_selector)
            if tab_element:
                print(f"使用主选择器找到标签 {tab_index}")
                # 点击前确保元素可见且可交互
                await tab_element.scroll_into_view_if_needed()
                await tab_element.click(timeout=10000) # 增加点击超时
                await page.wait_for_timeout(3000)  # 等待内容加载
                print(f"已切换到标签页 {tab_index}")
                return True

            # 如果主选择器失败，尝试备用选择器
            print(f"主选择器未找到标签 {tab_index}，尝试备用选择器...")
            tab_element = await page.query_selector(backup_selector)
            if tab_element:
                print(f"使用备用选择器找到标签 {tab_index}")
                # 点击前确保元素可见且可交互
                await tab_element.scroll_into_view_if_needed()
                await tab_element.click(timeout=10000) # 增加点击超时
                await page.wait_for_timeout(3000)  # 等待内容加载
                print(f"已切换到标签页 {tab_index}")
                return True

            print(f"❌ 无法找到标签 {tab_index}，主备选择器均失败")
            return False
        except Exception as e:
            print(f"切换到标签 {tab_index} 时出错: {e}")
            traceback.print_exc() # 打印详细错误堆栈
            return False

    async def _save_hero_equipment_data(self, hero_equip_data):
        """保存英雄装备数据"""
        if not hero_equip_data:
            return False
        
        try:
            cn_name = hero_equip_data["cn_name"]
            hero_cost = hero_equip_data.get("cost")
            
            # 创建按费用分类的子文件夹
            if hero_cost:
                cost_dir = os.path.join(self.data_structure['equips_dir'], f"{hero_cost}费")
                os.makedirs(cost_dir, exist_ok=True)
                file_path = os.path.join(cost_dir, f"{cn_name}.json")
            else:
                # 如果没有费用信息，则保存到英雄装备数据目录的根目录
                file_path = os.path.join(self.data_structure['equips_dir'], f"{cn_name}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json.dumps(hero_equip_data, ensure_ascii=False, indent=2))
            
            # 更新英雄装备映射
            hero_equip_mapping_file = os.path.join(self.data_structure['mapping_dir'], '英雄装备映射.json')
            hero_equip_mapping = {}
            
            # 如果映射文件存在，先读取现有映射
            if os.path.exists(hero_equip_mapping_file):
                try:
                    with open(hero_equip_mapping_file, 'r', encoding='utf-8') as f:
                        hero_equip_mapping = json.load(f)
                except:
                    hero_equip_mapping = {}
            
            # 查找此英雄的图标信息
            img_path = None
            for hero in self.heroes_data:
                if hero.get("cn_name") == cn_name:
                    # 检查英雄映射中是否已有图标路径
                    if cn_name in self.hero_name_mapping and "img_path" in self.hero_name_mapping[cn_name]:
                        img_path = self.hero_name_mapping[cn_name]["img_path"]
                    break
            
            # 更新映射，包含费用信息、相对路径和图标路径
            relative_path = f"{hero_cost}费/{cn_name}.json" if hero_cost else f"{cn_name}.json"
            
            # 如果英雄已存在于映射中，则更新
            if cn_name in hero_equip_mapping:
                hero_equip_mapping[cn_name].update({
                    "en_name": hero_equip_data["en_name"],
                    "cost": hero_cost,
                    "data_path": os.path.join("英雄装备数据", relative_path)
                })
                
                # 如果有图标路径但映射中没有，则添加
                if img_path and "img_path" not in hero_equip_mapping[cn_name]:
                    hero_equip_mapping[cn_name]["img_path"] = img_path
            else:
                # 创建新条目
                hero_equip_mapping[cn_name] = {
                    "en_name": hero_equip_data["en_name"],
                    "cn_name": cn_name,
                    "cost": hero_cost,
                    "data_path": os.path.join("英雄装备数据", relative_path)
                }
                
                # 如果有图标路径，则添加
                if img_path:
                    hero_equip_mapping[cn_name]["img_path"] = img_path
            
            # 保存映射
            with open(hero_equip_mapping_file, 'w', encoding='utf-8') as f:
                f.write(json.dumps(hero_equip_mapping, ensure_ascii=False, indent=2))
            
            print(f"英雄 {cn_name} 的装备数据已保存: {file_path}")
            return True
        except Exception as e:
            print(f"保存英雄装备数据时出错: {str(e)}")
            return False

    async def get_heroes_list(self, playwright):
        """
        获取英雄列表基本信息，包括中英文名称和费用

        参数:
            playwright: Playwright实例

        返回一个包含所有英雄信息的列表

        注意: 图标下载功能已移除，现在统一使用高清图标下载.py
        """
        print("开始爬取英雄列表")
        
        # 初始化浏览器
        browser = await playwright.chromium.launch(headless=True)
        
        try:
            # 创建新的页面上下文
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            # 打开新页面
            page = await context.new_page()
            
            # 设置超时时间
            page.set_default_timeout(30000)  # 30秒
            
            # 打开英雄列表页面
            list_url = self.base_url
            if self.use_latest:
                list_url = f"{self.base_url}/latest"
                
            print(f"打开英雄列表页面: {list_url}")
            try:
                # 尝试不同的等待策略
                response = await page.goto(list_url, wait_until="domcontentloaded", timeout=30000)
                if response.status != 200:
                    print(f"页面响应状态码: {response.status}")
            except Exception as e:
                print(f"页面导航错误详情: {str(e)}")
                raise
            
            # 添加更多的中间状态检查
            try:
                # 等待页面内容加载
                print("等待页面基本内容加载...")
                await page.wait_for_selector("body", timeout=10000)
                print("基本内容已加载，等待主要内容元素...")
                
                # 等待主要内容容器
                await page.wait_for_selector("#content-container", timeout=20000)
                html = await page.content()
                print(f"页面内容长度: {len(html)}")
            except Exception as e:
                print(f"等待页面元素错误: {str(e)}")
                # 尝试获取当前页面源码来分析问题
                try:
                    html = await page.content()
                    print(f"当前页面内容片段: {html[:500]}...")
                except:
                    pass
                raise
            
            # 等待确保数据完全加载
            await asyncio.sleep(2)
            
            # 遍历不同费用的英雄
            for cost, selector in self.selectors['cost_containers'].items():
                print(f"处理 {cost} 费英雄...")
                
                try:
                    # 获取对应费用的容器
                    container = await page.query_selector(selector)
                    if not container:
                        print(f"未找到 {cost} 费英雄容器，请检查选择器是否变更")
                        continue
                    
                    # 获取英雄元素
                    hero_elements = await container.query_selector_all(self.selectors['hero_container'])
                    print(f"找到 {len(hero_elements)} 个 {cost} 费英雄")
                    
                    # 遍历英雄元素
                    for idx, hero_element in enumerate(hero_elements, 1):
                        try:
                            # 获取英雄英文名称（从href中提取）
                            href = await hero_element.get_attribute("href")
                            if not href:
                                continue
                            
                            # 修复英文名提取问题，处理使用latest时的情况
                            href_parts = href.split("/")
                            en_name = href_parts[-1]
                            
                            # 如果获取到的是"latest"，则使用前一个部分作为英雄名
                            if en_name == "latest":
                                en_name = href_parts[-2]
                            
                            if not en_name:
                                continue
                            
                            # 获取英雄中文名称
                            cn_name_element = await hero_element.query_selector(self.selectors['hero_cn_name'])
                            if not cn_name_element:
                                continue
                            
                            cn_name = await cn_name_element.text_content()
                            cn_name = cn_name.strip()
                            
                            # 获取英雄图标URL
                            icon_url = await self._construct_hero_icon_url(en_name)
                            
                            # 构建相对路径用于映射文件
                            file_name = f"{cn_name}.png"
                            relative_path = os.path.join("英雄图标", f"{cost}费", file_name) # 相对路径
                            
                            # 构建英雄数据
                            hero_data = {
                                "en_name": en_name,
                                "cn_name": cn_name,
                                "cost": cost,
                                "icon_url": icon_url,
                                "detail_url": f"{self.base_url}/{en_name}" + ("/latest" if self.use_latest else ""),
                                "img_path": relative_path # 添加相对路径
                            }
                            
                            self.heroes_data.append(hero_data)
                            
                            # 更新英雄名称映射
                            self.hero_name_mapping[cn_name] = {
                                "en_name": en_name,
                                "cn_name": cn_name,
                                "cost": cost,
                                "icon_url": icon_url,
                                "img_path": relative_path # 添加相对路径
                            }
                            
                            # 图标下载功能已移除，现在统一使用高清图标下载.py
                            
                            print(f"成功提取英雄信息 [{idx}/{len(hero_elements)}]: {cn_name} / {en_name} ({cost}费)")
                            
                            # 添加随机延迟
                            await self.add_random_delay()
                            
                        except Exception as e:
                            print(f"提取第 {idx} 个 {cost} 费英雄信息时出错: {str(e)}")
                    
                except Exception as e:
                    print(f"处理 {cost} 费英雄时出错: {str(e)}")
            
            # 关闭页面和上下文
            await page.close()
            await context.close()
            
            # 保存数据
            await self.save_data_to_json()
            
            print(f"英雄列表爬取完成，共 {len(self.heroes_data)} 个英雄")
            
            return self.heroes_data
            
        except Exception as e:
            print(f"爬取英雄列表时出错: {str(e)}")
            return []
            
        finally:
            # 确保关闭浏览器
            await browser.close()

async def main(max_concurrent=3, base_dir=None, skip_equips=False,
               fetch_heroes=True, min_delay=0.5, max_delay=1.0, use_latest=False):
    """
    主函数 - 支持作为子模块调用

    参数:
        max_concurrent: 最大并发数量
        base_dir: 基础数据保存目录
        skip_equips: 是否跳过装备数据爬取
        fetch_heroes: 是否爬取英雄列表和基础信息
        min_delay: 随机延迟最小值(秒)
        max_delay: 随机延迟最大值(秒)
        use_latest: 是否使用最新数据 (/latest 后缀)

    返回:
        成功时返回0，失败时返回非零值

    注意: 图标下载功能已移除，现在统一使用高清图标下载.py
    """
    print("=" * 50)
    print("云顶之奕英雄装备数据爬取")
    print("=" * 50)
    
    # 最大重试次数
    max_retries = 3
    retry_count = 0
    scraper = None
    
    try:
        # 使用更低的初始并发数，如果运行顺利再逐步提高
        current_concurrent = min(max_concurrent, 2)  # 先用较低的并发开始
        
        # 创建爬虫实例
        scraper = HeroEquipmentScraper(
            base_dir=base_dir,
            max_concurrent=current_concurrent,
            min_delay=min_delay,
            max_delay=max_delay,
            use_latest=use_latest
        )
        
        # 运行爬虫
        print("开始爬取云顶之奕英雄装备数据，请稍候...")
        
        start_time = time.time()
        
        # 先根据选项清理对应数据
        await scraper.clean_selected_data(
            clean_heroes=fetch_heroes,
            clean_icons=False,  # 不再清理图标，因为图标下载已移除
            clean_equips=not skip_equips
        )

        # 尝试运行，出错时可以重试
        while retry_count < max_retries:
            try:
                # 运行爬虫
                success = await scraper.run(
                    skip_equips=skip_equips,
                    fetch_heroes=fetch_heroes
                )
                
                if success:
                    break  # 成功完成，退出循环
                else:
                    retry_count += 1
                    print(f"\n爬取未完全成功，尝试重试 ({retry_count}/{max_retries})")
                    if retry_count < max_retries:
                        # 降低并发数，提高成功率
                        current_concurrent = max(1, current_concurrent - 1)
                        scraper.max_concurrent = current_concurrent
                        print(f"降低并发数到 {current_concurrent} 以提高稳定性")
                        
                        wait_time = 15 * retry_count
                        print(f"将在 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        print(f"已达到最大重试次数 ({max_retries})，程序终止")
                        return 1
                
            except Exception as e:
                retry_count += 1
                print(f"\n错误: {str(e)}")
                traceback.print_exc()
                
                if retry_count < max_retries:
                    # 降低并发数，提高成功率
                    current_concurrent = max(1, current_concurrent - 1)
                    scraper.max_concurrent = current_concurrent
                    print(f"降低并发数到 {current_concurrent} 以提高稳定性")
                    
                    wait_time = 20 * retry_count
                    print(f"将在 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"已达到最大重试次数 ({max_retries})，程序终止")
                    return 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        if scraper and scraper.heroes_data: # 检查 scraper 是否成功创建且有数据
             print(f"英雄装备数据爬取完成！共 {len(scraper.heroes_data)} 个英雄")
        else:
             print("英雄装备数据爬取完成，但未能获取到英雄数据。")
        print(f"总耗时: {duration:.2f} 秒")
        print("=" * 50)
        
        return 0 # 成功完成
        
    except Exception as e:
        print(f"\n严重错误: {str(e)}")
        traceback.print_exc()
        return 1
        
    finally:
        print("主函数执行完毕。") # 可以添加一条完成信息

# 添加一个同步的函数，便于从其他模块调用
def run_hero_equipment_scraper(max_concurrent=3, base_dir=None, skip_equips=False, timeout=None, # timeout 参数保留，但不再强制执行
                               download_icons=True, fetch_heroes=True, gui=False, min_delay=0.5, max_delay=1.0, use_latest=False,
                               force_update=False):
    """运行英雄装备数据爬虫

    Args:
        max_concurrent: 最大并发数
        base_dir: 基础目录
        skip_equips: 是否跳过装备数据爬取
        timeout: 超时时间（已弃用，保留参数兼容性）
        download_icons: 是否下载图标（已弃用，保留参数兼容性）
        fetch_heroes: 是否爬取英雄列表
        gui: 是否使用图形界面
        min_delay: 最小延迟
        max_delay: 最大延迟
        use_latest: 是否使用最新数据
        force_update: 是否强制更新数据（忽略本地缓存）

    Returns:
        bool: 是否成功

    注意：图标下载功能已移除，现在统一使用高清图标下载.py
    """
    try:
        if gui:
            # GUI模式下的代码
            # ... 保持原有代码 ...
            pass
        else:
            print(f"开始爬取英雄装备数据")
            print(f"配置: 最大并发={max_concurrent}, 跳过装备={skip_equips}, 爬取英雄列表={fetch_heroes}")
            print(f"注意: 图标下载功能已移除，请使用高清图标下载.py统一处理")

            # 初始化基础目录
            current_dir = base_dir or os.path.dirname(os.path.abspath(__file__))

            # 如果是强制更新模式，只删除映射文件
            if force_update:
                mapping_dir = os.path.join(current_dir, "映射文件")
                hero_mapping_file = os.path.join(mapping_dir, "英雄装备映射.json")

                if os.path.exists(hero_mapping_file):
                    os.remove(hero_mapping_file)
                    print(f"强制更新模式：已删除旧的英雄装备映射文件")

            # 使用 asyncio.run 运行异步爬虫
            result = asyncio.run(main(
                max_concurrent=max_concurrent,
                base_dir=current_dir,
                skip_equips=skip_equips,
                fetch_heroes=fetch_heroes or force_update,  # 强制更新时也重新爬取英雄列表
                min_delay=min_delay,
                max_delay=max_delay,
                use_latest=use_latest
            ))
            
            # 修复返回值类型：将整数转换为布尔值
            return result == 0  # 0表示成功，返回True；非0表示失败，返回False
    except Exception as e:
        print(f"英雄装备爬虫运行时出错: {str(e)}")
        traceback.print_exc()
        return False

# 修改英雄数据获取的统一函数
def get_hero_data(base_dir=None, download_icons=False, fetch_heroes=False, use_latest=True, force_update=False):
    """
    获取英雄数据的同步函数，返回英雄数据映射

    Args:
        base_dir: 基础目录
        download_icons: 是否下载英雄图标（已弃用，保留参数兼容性）
        fetch_heroes: 是否爬取新的英雄数据(否则使用已有数据)
        use_latest: 是否使用最新数据
        force_update: 是否强制更新数据（忽略本地缓存）

    Returns:
        tuple: (是否成功, 英雄映射数据)

    注意: 图标下载功能已移除，现在统一使用高清图标下载.py
    """
    try:
        # 确定基础目录
        current_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        mapping_file = os.path.join(current_dir, "映射文件", "英雄装备映射.json")
        
        # 如果文件已经存在，且不是强制更新模式，直接加载
        if not force_update and os.path.exists(mapping_file) and not fetch_heroes:
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    hero_data = json.load(f)
                print(f"从本地文件加载了英雄数据")
                return True, hero_data
            except Exception as e:
                print(f"读取本地英雄数据失败，将重新获取: {e}")
        
        # 重新获取数据
        success = run_hero_equipment_scraper(
            max_concurrent=3,
            base_dir=current_dir,
            skip_equips=True,  # 只获取英雄数据，不获取装备数据
            download_icons=False,  # 图标下载功能已移除
            fetch_heroes=fetch_heroes or force_update,  # 强制更新时也重新爬取英雄列表
            gui=False,
            min_delay=0.5,
            max_delay=1.0,
            use_latest=use_latest,
            force_update=force_update
        )
        
        # 如果成功获取，读取新生成的文件
        if success and os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                hero_data = json.load(f)
            return True, hero_data
        else:
            return False, {}
    
    except Exception as e:
        print(f"获取英雄数据时出错: {e}")
        return False, {}

# 如果直接运行此脚本
if __name__ == "__main__":
    # 添加命令行参数支持
    import argparse
    parser = argparse.ArgumentParser(description='英雄数据爬取工具')
    parser.add_argument('--force', action='store_true', help='强制重新爬取数据')
    parser.add_argument('--icons', action='store_true', help='下载图标')
    parser.add_argument('--equips', action='store_true', help='爬取装备数据')
    
    args = parser.parse_args()
    
    if args.equips:
        # 获取英雄装备数据
        success = run_hero_equipment_scraper(
            download_icons=args.icons,
            fetch_heroes=True,
            force_update=args.force
        )
    else:
        # 只获取英雄基础数据
        success, _ = get_hero_data(
            download_icons=args.icons,
            fetch_heroes=True,
            force_update=args.force
        )
    
    if success:
        print("英雄数据获取成功！")
    else:
        print("英雄数据获取失败！")