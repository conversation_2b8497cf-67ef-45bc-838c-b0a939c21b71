# [关键] 在导入任何模块之前禁用字节码生成
import sys
sys.dont_write_bytecode = True

import os
import time
import re
import threading
import sqlite3
import logging
import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from PIL import ImageGrab, Image, ImageEnhance
from pypinyin import lazy_pinyin
from Levenshtein import ratio
import requests
import json
import hashlib
import atexit
import socket
import tempfile
import imagehash
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import cv2
import configparser
import argparse
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import config # 导入新的配置模块
import utils # 导入新的工具模块
import ocr_handler # 导入新的OCR处理模块
import data_query # 导入新的数据查询模块
import managers # 导入新的管理器模块
import logger_setup # 导入日志设置模块
import instance_checker # 导入单例检查模块
import updater # 导入更新模块


# DPI Awareness (保持不变)
if sys.platform == 'win32':
    import ctypes
    try:
        ctypes.windll.shcore.SetProcessDpiAwareness(2)
    except AttributeError:
        try:
            ctypes.windll.user32.SetProcessDPIAware()
        except AttributeError:
            pass



# 删除了错误的HeadlessOCRCore类，直接使用TFTAssistantApp的隐藏模式

# --- API处理器 ---
class OCRAPIHandler(BaseHTTPRequestHandler):
    """处理来自前端的API请求"""

    def __init__(self, *args, app=None, **kwargs):
        self.app = app
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path == '/api/status':
            self._handle_get_status()
        else:
            self._send_error(404, "Not Found")

    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)

        if parsed_path.path == '/api/scan_hex':
            self._handle_scan_hex()
        elif parsed_path.path == '/api/scan_equipment':
            self._handle_scan_equipment()
        elif parsed_path.path == '/api/scan_fruit':
            self._handle_scan_fruit()
        elif parsed_path.path == '/api/toggle_auto':
            self._handle_toggle_auto()
        elif parsed_path.path == '/api/show_window':
            self._handle_show_window()
        elif parsed_path.path == '/api/hide_window':
            self._handle_hide_window()
        else:
            self._send_error(404, "Not Found")

    def _handle_get_status(self):
        """获取状态"""
        if self.app:
            status = {
                'auto_mode': bool(self.app.trigger_manager and self.app.trigger_manager.is_monitoring),
                'components_ready': bool(self.app.hex_manager and self.app.equip_manager and self.app.trigger_manager),
                'hex_manager_ready': bool(self.app.hex_manager),
                'equip_manager_ready': bool(self.app.equip_manager),
                'trigger_manager_ready': bool(self.app.trigger_manager),
                'monitoring_active': bool(self.app.trigger_manager and self.app.trigger_manager.is_monitoring),
                'hidden_mode': self.app.hidden_mode,
                'timestamp': time.time()
            }
            self._send_json_response(status)
        else:
            self._send_error(500, "App not available")

    def _handle_scan_hex(self):
        """手动扫描海克斯"""
        if self.app and self.app.hex_manager:
            try:
                result = self.app.hex_manager.start_hex_ocr(manual=True)
                self._send_json_response({'result': result, 'success': True})
            except Exception as e:
                self._send_json_response({'result': str(e), 'success': False})
        else:
            self._send_error(500, "Hex Manager not available")

    def _handle_scan_equipment(self):
        """手动扫描装备"""
        if self.app and self.app.equip_manager:
            try:
                result = self.app.equip_manager.start_equip_ocr(manual=True)
                self._send_json_response({'result': result, 'success': True})
            except Exception as e:
                self._send_json_response({'result': str(e), 'success': False})
        else:
            self._send_error(500, "Equipment Manager not available")

    def _handle_scan_fruit(self):
        """手动扫描果实"""
        if self.app and self.app.fruit_manager:
            try:
                result = self.app.fruit_manager.start_fruit_ocr(manual=True)
                self._send_json_response({'result': result, 'success': True})
            except Exception as e:
                self._send_json_response({'result': str(e), 'success': False})
        else:
            self._send_error(500, "Fruit Manager not available")

    def _handle_toggle_auto(self):
        """切换自动模式"""
        if self.app and self.app.trigger_manager:
            try:
                if self.app.trigger_manager.is_monitoring:
                    self.app.trigger_manager.stop_monitoring()
                    action = 'stopped'
                    auto_mode = False
                else:
                    self.app.trigger_manager.start_monitoring()
                    action = 'started'
                    auto_mode = True

                self._send_json_response({
                    'success': True,
                    'action': action,
                    'auto_mode': auto_mode
                })
            except Exception as e:
                self._send_json_response({'success': False, 'error': str(e)})
        else:
            self._send_error(500, "Trigger Manager not available")

    def _handle_show_window(self):
        """显示窗口"""
        if self.app:
            try:
                self.app.show_window()
                self._send_json_response({'success': True, 'action': 'window_shown'})
            except Exception as e:
                self._send_json_response({'success': False, 'error': str(e)})
        else:
            self._send_error(500, "App not available")

    def _handle_hide_window(self):
        """隐藏窗口"""
        if self.app:
            try:
                self.app.hide_window()
                self._send_json_response({'success': True, 'action': 'window_hidden'})
            except Exception as e:
                self._send_json_response({'success': False, 'error': str(e)})
        else:
            self._send_error(500, "App not available")

    def _send_json_response(self, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False).encode('utf-8')
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response)))
        self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域
        self.end_headers()
        self.wfile.write(response)

    def _send_error(self, code, message):
        """发送错误响应"""
        self.send_response(code)
        self.send_header('Content-Type', 'text/plain; charset=utf-8')
        self.end_headers()
        self.wfile.write(message.encode('utf-8'))

    def log_message(self, format, *args):
        """重写日志方法，使用我们的logger"""
        logger = logging.getLogger('API')
        logger.info(f"{self.address_string()} - {format % args}")

# --- 主应用和GUI ---
class TFTAssistantApp(tk.Tk):
    def __init__(self, hidden=False):
        super().__init__()
        self.title("弈秒决")
        self.configure(bg="#0A0E14")  # 主窗口背景色 (深色)
        self.screen_width = self.winfo_screenwidth()
        self.screen_height = self.winfo_screenheight()
        self.scale = min(self.winfo_screenwidth() / 2560, self.winfo_screenheight() / 1440)
        self.buttons_visible = False  # 初始状态：隐藏子按钮
        self.drag_active = False  # 添加拖拽状态标志
        self.debug_mode = config.DEBUG_MODE # [修改] 从配置文件读取调试模式
        self.hidden_mode = hidden  # 是否为隐藏模式
        self.api_server = None  # API服务器实例
        
        # 修正顺序 1: 首先配置样式和字体
        self._configure_styles()
        
        # 修正顺序 2: 初始化管理器，它依赖样式
        # 获取日志系统实例
        from logger_setup import get_logging_system
        logging_system = get_logging_system()
        
        # 初始化海克斯管理器
        logging_system.log_manager_initialization('HexManager', 'starting')
        try:
            self.hex_manager = managers.HexManager(self)
            logging_system.log_manager_initialization('HexManager', 'success')
        except Exception as e:
            logging_system.log_manager_initialization('HexManager', 'failed', str(e))
            raise
        
        # 初始化装备管理器
        logging_system.log_manager_initialization('EquipManager', 'starting')
        try:
            self.equip_manager = managers.EquipManager(self)
            logging_system.log_manager_initialization('EquipManager', 'success')
        except Exception as e:
            logging_system.log_manager_initialization('EquipManager', 'failed', str(e))
            raise

        # 初始化果实管理器
        logging_system.log_manager_initialization('FruitManager', 'starting')
        try:
            self.fruit_manager = managers.FruitManager(self)
            logging_system.log_manager_initialization('FruitManager', 'success')
        except Exception as e:
            logging_system.log_manager_initialization('FruitManager', 'failed', str(e))
            raise

        # 初始化触发器管理器
        logging_system.log_manager_initialization('TriggerManager', 'starting')
        try:
            self.trigger_manager = managers.TriggerManager(self, self.hex_manager, self.equip_manager, self.fruit_manager) # [新] 初始化中央触发器
            logging_system.log_manager_initialization('TriggerManager', 'success')
        except Exception as e:
            logging_system.log_manager_initialization('TriggerManager', 'failed', str(e))
            raise
        
        # 修正顺序 3: 初始化主UI，它依赖管理器
        self._init_ui()

        self.protocol("WM_DELETE_WINDOW", self.on_close) # 系统关闭按钮

        # 如果是隐藏模式，启动API服务器
        if self.hidden_mode:
            self._start_api_server()
        else:
            # 初始化完成后显示无边框提示
            self.after(100, self.show_borderless_tip)
        
        # 初始化完成后自动启动轮次检测
        self.after(1000, self.trigger_manager.start_monitoring) # [新] 启动中央触发器

        # [新增] 初始化并绑定右键菜单
        if not self.hidden_mode:
            self.setup_context_menu()

    def _start_api_server(self, port=8888):
        """启动API服务器"""
        try:
            # 创建API处理器工厂
            def handler_factory(*args, **kwargs):
                return OCRAPIHandler(*args, app=self, **kwargs)

            # 启动HTTP服务器
            self.api_server = HTTPServer(('localhost', port), handler_factory)

            # 在后台线程运行服务器
            def run_server():
                logging.info(f"API服务器启动在 http://localhost:{port}")
                self.api_server.serve_forever()

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            logging.info(f"隐藏模式API服务器已启动，端口: {port}")

        except Exception as e:
            logging.error(f"API服务器启动失败: {e}")

    def show_window(self):
        """显示窗口"""
        if self.hidden_mode:
            self.deiconify()
            self.lift()
            self.attributes('-topmost', True)

    def hide_window(self):
        """隐藏窗口"""
        if self.hidden_mode:
            self.withdraw()

    def _init_ui(self):
        if self.hidden_mode:
            # 隐藏模式：创建最小化窗口但不显示
            self.withdraw()  # 隐藏窗口
            return

        self.overrideredirect(True)  # 去除窗口边框
        self.attributes('-topmost', True)  # 置顶
        self.config(highlightthickness=0, bd=0)

        self.btn_width = 229
        self.btn_height = 68
        self.button_bg_color = "#6495ED"          # 按钮背景色 (蓝色)
        self.button_fg_color = "white"            # 按钮文字颜色 (白色)
        self.button_hover_color = "#4682B4"       # 悬停颜色 (深蓝色)
        self.button_active_color = "#4682B4"      # 点击颜色 (与悬停相同)
        self.button_font = ("LXGW WenKai Mono", 17, "bold")  # 字体
        self.separator_color = "black"             # 分隔线颜色

        total_height = self.btn_height * 4 + 3 # 总高度调整为4个按钮
        visible_height = total_height if self.buttons_visible else self.btn_height

        # 根据按钮可见性设置canvas高度
        self.canvas = tk.Canvas(self,
                                width=self.btn_width,
                                height=visible_height,
                                bg=self.button_bg_color,  # 使用按钮背景色
                                highlightthickness=0)
        self.canvas.pack()

        # --- 主按钮 (弈秒决) ---
        self.main_button_rect = self.canvas.create_rectangle(0, 0, self.btn_width, self.btn_height,
                                                            fill=self.button_bg_color, outline="")
        self.main_text_id = self.canvas.create_text(self.btn_width / 2 - 10, self.btn_height / 2,  # 微调位置
                                                    text="☰ 弈秒决",
                                                    fill=self.button_fg_color,
                                                    font=self.button_font,
                                                    anchor="center")
        # 关闭按钮 (右上角)
        self.close_button_rect = self.canvas.create_rectangle(self.btn_width - 35, 0, self.btn_width, 25,
                                                              fill=self.button_bg_color, outline="")
        self.close_text_id = self.canvas.create_text(self.btn_width - 17, 12, text="X",
                                                      fill=self.button_fg_color, font=("Arial", 12, "bold"),
                                                      anchor="center")

        # --- 分隔线 1 ---
        self.separator1 = self.canvas.create_line(0, self.btn_height, self.btn_width, self.btn_height,
                                                 fill=self.separator_color, width=1)

        # --- 子按钮 (海克斯) ---
        self.hex_button_rect = self.canvas.create_rectangle(0, self.btn_height + 1, self.btn_width, self.btn_height * 2 + 1,
                                                            fill=self.button_bg_color, outline="")
        self.hex_text_id = self.canvas.create_text(self.btn_width / 2, self.btn_height * 1.5 + 1,
                                                  text="▶ 海克斯 ◀",
                                                  fill=self.button_fg_color,
                                                  font=self.button_font,
                                                  anchor="center")

        # --- 分隔线 2 ---
        self.separator2 = self.canvas.create_line(0, self.btn_height * 2 + 1, self.btn_width, self.btn_height * 2 + 1,
                                                 fill=self.separator_color, width=1)

        # --- 子按钮 (装备) ---
        self.equip_button_rect = self.canvas.create_rectangle(0, self.btn_height * 2 + 2, self.btn_width, self.btn_height * 3 + 2,
                                                              fill=self.button_bg_color, outline="")
        self.equip_text_id = self.canvas.create_text(self.btn_width / 2, self.btn_height * 2.5 + 2,
                                                    text="▶  装备  ◀",
                                                    fill=self.button_fg_color,
                                                    font=self.button_font,
                                                    anchor="center")

        # --- 分隔线 3 ---
        self.separator3 = self.canvas.create_line(0, self.btn_height * 3 + 2, self.btn_width, self.btn_height * 3 + 2,
                                                 fill=self.separator_color, width=1)

        # --- 子按钮 (果实) ---
        self.fruit_button_rect = self.canvas.create_rectangle(0, self.btn_height * 3 + 3, self.btn_width, self.btn_height * 4 + 3,
                                                              fill=self.button_bg_color, outline="")
        self.fruit_text_id = self.canvas.create_text(self.btn_width / 2, self.btn_height * 3.5 + 3,
                                                    text="▶  果实  ◀",
                                                    fill=self.button_fg_color,
                                                    font=self.button_font,
                                                    anchor="center")

        # --- 分隔线 4 ---
        self.separator4 = self.canvas.create_line(0, self.btn_height * 4 + 3, self.btn_width, self.btn_height * 4 + 3,
                                                 fill=self.separator_color, width=1)

        # --- 子按钮 (打开日志) ---
        self.log_button_rect = self.canvas.create_rectangle(0, self.btn_height * 4 + 4, self.btn_width, self.btn_height * 5 + 4,
                                                            fill=self.button_bg_color, outline="")
        self.log_text_id = self.canvas.create_text(self.btn_width / 2, self.btn_height * 4.5 + 4,
                                                  text="▶ 打开日志 ◀",
                                                  fill=self.button_fg_color,
                                                  font=self.button_font,
                                                  anchor="center")


        # 设置窗口大小 (根据canvas大小)
        window_width = self.btn_width
        window_height = visible_height
        self.geometry(f"{window_width}x{window_height}+0+0")  # 初始位置在左上角

        # 初始化拖动数据
        self.drag_data = {"x": 0, "y": 0}

        # --- 绑定事件 ---
        self.canvas.tag_bind(self.main_button_rect, "<ButtonPress-1>", self.start_drag)
        self.canvas.tag_bind(self.main_text_id, "<ButtonPress-1>", self.start_drag)
        self.canvas.tag_bind(self.close_button_rect, "<Button-1>", self.on_close)
        self.canvas.tag_bind(self.close_text_id, "<Button-1>", self.on_close)
        
        self.canvas.tag_bind(self.main_button_rect, "<ButtonRelease-1>", self.on_main_button_release)
        self.canvas.tag_bind(self.main_text_id, "<ButtonRelease-1>", self.on_main_button_release)
        
        self.canvas.tag_bind(self.main_button_rect, "<B1-Motion>", self.on_drag)
        self.canvas.tag_bind(self.main_text_id, "<B1-Motion>", self.on_drag)

        self.canvas.tag_bind(self.main_button_rect, "<Enter>", lambda e: self.on_enter(self.main_button_rect))
        self.canvas.tag_bind(self.main_text_id, "<Enter>", lambda e: self.on_enter(self.main_button_rect))
        self.canvas.tag_bind(self.main_button_rect, "<Leave>", lambda e: self.on_leave(self.main_button_rect))
        self.canvas.tag_bind(self.main_text_id, "<Leave>", lambda e: self.on_leave(self.main_button_rect))

        self.canvas.tag_bind(self.close_button_rect, "<Enter>", lambda e: self.on_enter(self.close_button_rect, is_close=True))
        self.canvas.tag_bind(self.close_text_id, "<Enter>", lambda e: self.on_enter(self.close_button_rect, is_close=True))
        self.canvas.tag_bind(self.close_button_rect, "<Leave>", lambda e: self.on_leave(self.close_button_rect, is_close=True))
        self.canvas.tag_bind(self.close_text_id, "<Leave>", lambda e: self.on_leave(self.close_button_rect, is_close=True))

        self.canvas.tag_bind(self.hex_button_rect, "<Enter>", lambda e: self.on_enter(self.hex_button_rect))
        self.canvas.tag_bind(self.hex_text_id, "<Enter>", lambda e: self.on_enter(self.hex_button_rect))
        self.canvas.tag_bind(self.hex_button_rect, "<Leave>", lambda e: self.on_leave(self.hex_button_rect))
        self.canvas.tag_bind(self.hex_text_id, "<Leave>", lambda e: self.on_leave(self.hex_button_rect))
        self.canvas.tag_bind(self.hex_button_rect, "<Button-1>", lambda e: self.hex_manager.start_hex_ocr(manual=True))
        self.canvas.tag_bind(self.hex_text_id, "<Button-1>", lambda e: self.hex_manager.start_hex_ocr(manual=True))

        self.canvas.tag_bind(self.equip_button_rect, "<Enter>", lambda e: self.on_enter(self.equip_button_rect))
        self.canvas.tag_bind(self.equip_text_id, "<Enter>", lambda e: self.on_enter(self.equip_button_rect))
        self.canvas.tag_bind(self.equip_button_rect, "<Leave>", lambda e: self.on_leave(self.equip_button_rect))
        self.canvas.tag_bind(self.equip_text_id, "<Leave>", lambda e: self.on_leave(self.equip_button_rect))
        self.canvas.tag_bind(self.equip_button_rect, "<Button-1>", lambda e: self.equip_manager.start_equip_ocr(manual=True))
        self.canvas.tag_bind(self.equip_text_id, "<Button-1>", lambda e: self.equip_manager.start_equip_ocr(manual=True))

        # --- 果实按钮事件绑定 ---
        self.canvas.tag_bind(self.fruit_button_rect, "<Enter>", lambda e: self.on_enter(self.fruit_button_rect))
        self.canvas.tag_bind(self.fruit_text_id, "<Enter>", lambda e: self.on_enter(self.fruit_button_rect))
        self.canvas.tag_bind(self.fruit_button_rect, "<Leave>", lambda e: self.on_leave(self.fruit_button_rect))
        self.canvas.tag_bind(self.fruit_text_id, "<Leave>", lambda e: self.on_leave(self.fruit_button_rect))
        self.canvas.tag_bind(self.fruit_button_rect, "<Button-1>", lambda e: self.fruit_manager.start_fruit_ocr(manual=True))
        self.canvas.tag_bind(self.fruit_text_id, "<Button-1>", lambda e: self.fruit_manager.start_fruit_ocr(manual=True))

        # --- 日志按钮事件绑定 ---
        self.canvas.tag_bind(self.log_button_rect, "<Enter>", lambda e: self.on_enter(self.log_button_rect))
        self.canvas.tag_bind(self.log_text_id, "<Enter>", lambda e: self.on_enter(self.log_button_rect))
        self.canvas.tag_bind(self.log_button_rect, "<Leave>", lambda e: self.on_leave(self.log_button_rect))
        self.canvas.tag_bind(self.log_text_id, "<Leave>", lambda e: self.on_leave(self.log_button_rect))
        self.canvas.tag_bind(self.log_button_rect, "<Button-1>", self.open_log_directory)
        self.canvas.tag_bind(self.log_text_id, "<Button-1>", self.open_log_directory)

        # 如果初始状态是隐藏子按钮，则隐藏分隔线和子按钮
        if not self.buttons_visible:
            # 隐藏分隔线和子按钮
            self.canvas.itemconfig(self.separator1, state="hidden")
            self.canvas.itemconfig(self.separator2, state="hidden")
            self.canvas.itemconfig(self.separator3, state="hidden") # 隐藏分隔线3
            self.canvas.itemconfig(self.separator4, state="hidden") # 隐藏分隔线4
            self.canvas.itemconfig(self.hex_button_rect, state="hidden")
            self.canvas.itemconfig(self.hex_text_id, state="hidden")
            self.canvas.itemconfig(self.equip_button_rect, state="hidden")
            self.canvas.itemconfig(self.equip_text_id, state="hidden")
            self.canvas.itemconfig(self.fruit_button_rect, state="hidden") # 隐藏果实按钮
            self.canvas.itemconfig(self.fruit_text_id, state="hidden") # 隐藏果实按钮文本
            self.canvas.itemconfig(self.log_button_rect, state="hidden") # 隐藏日志按钮
            self.canvas.itemconfig(self.log_text_id, state="hidden") # 隐藏日志按钮文本

    def on_enter(self, rect_id, is_close=False):
        if is_close:
          self.canvas.itemconfig(rect_id, fill="#FF6347")  # 关闭按钮悬停: 红色
          self.canvas.itemconfig(self.close_text_id, fill="white")
        else:
            self.canvas.itemconfig(rect_id, fill=self.button_hover_color)  # 其他按钮悬停
            # 找到对应的文本，更改颜色
            if rect_id == self.main_button_rect:
                self.canvas.itemconfig(self.main_text_id, fill="#E0E0E0")
            elif rect_id == self.hex_button_rect:
                self.canvas.itemconfig(self.hex_text_id, fill="#E0E0E0")
            elif rect_id == self.equip_button_rect:
                self.canvas.itemconfig(self.equip_text_id, fill=self.button_fg_color)
            elif rect_id == self.fruit_button_rect:
                self.canvas.itemconfig(self.fruit_text_id, fill="#E0E0E0")
            elif rect_id == self.log_button_rect:
                self.canvas.itemconfig(self.log_text_id, fill="#E0E0E0")


    def on_leave(self, rect_id, is_close=False):
        if is_close:
            self.canvas.itemconfig(rect_id, fill=self.button_bg_color)  # 关闭按钮恢复
            self.canvas.itemconfig(self.close_text_id, fill=self.button_fg_color)
        else:
            self.canvas.itemconfig(rect_id, fill=self.button_bg_color)  # 其他按钮恢复
            # 恢复文本颜色
            if rect_id == self.main_button_rect:
                self.canvas.itemconfig(self.main_text_id, fill=self.button_fg_color)
            elif rect_id == self.hex_button_rect:
                self.canvas.itemconfig(self.hex_text_id, fill=self.button_fg_color)
            elif rect_id == self.equip_button_rect:
                self.canvas.itemconfig(self.equip_text_id, fill=self.button_fg_color)
            elif rect_id == self.fruit_button_rect:
                self.canvas.itemconfig(self.fruit_text_id, fill=self.button_fg_color)
            elif rect_id == self.log_button_rect:
                self.canvas.itemconfig(self.log_text_id, fill=self.button_fg_color)

    def toggle_buttons(self, event):
        self.buttons_visible = not self.buttons_visible
        if self.buttons_visible:
            # 显示子按钮和分隔线
            self.canvas.itemconfig(self.separator1, state=tk.NORMAL)
            self.canvas.itemconfig(self.hex_button_rect, state=tk.NORMAL)
            self.canvas.itemconfig(self.hex_text_id, state=tk.NORMAL)
            self.canvas.itemconfig(self.separator2, state=tk.NORMAL)
            self.canvas.itemconfig(self.equip_button_rect, state=tk.NORMAL)
            self.canvas.itemconfig(self.equip_text_id, state=tk.NORMAL)
            self.canvas.itemconfig(self.separator3, state=tk.NORMAL) # 显示分隔线3
            self.canvas.itemconfig(self.fruit_button_rect, state=tk.NORMAL) # 显示果实按钮
            self.canvas.itemconfig(self.fruit_text_id, state=tk.NORMAL) # 显示果实按钮文本
            self.canvas.itemconfig(self.separator4, state=tk.NORMAL) # 显示分隔线4
            self.canvas.itemconfig(self.log_button_rect, state=tk.NORMAL) # 显示日志按钮
            self.canvas.itemconfig(self.log_text_id, state=tk.NORMAL) # 显示日志按钮文本
            self.canvas.config(height=self.btn_height * 5 + 4) # 更新高度
            self.geometry(f"{self.btn_width}x{self.btn_height * 5 + 4}")  # 更新窗口高度
        else:
            # 隐藏子按钮和分隔线
            self.canvas.itemconfig(self.separator1, state=tk.HIDDEN)
            self.canvas.itemconfig(self.hex_button_rect, state=tk.HIDDEN)
            self.canvas.itemconfig(self.hex_text_id, state=tk.HIDDEN)
            self.canvas.itemconfig(self.separator2, state=tk.HIDDEN)
            self.canvas.itemconfig(self.equip_button_rect, state=tk.HIDDEN)
            self.canvas.itemconfig(self.equip_text_id, state=tk.HIDDEN)
            self.canvas.itemconfig(self.separator3, state=tk.HIDDEN) # 隐藏分隔线3
            self.canvas.itemconfig(self.fruit_button_rect, state=tk.HIDDEN) # 隐藏果实按钮
            self.canvas.itemconfig(self.fruit_text_id, state=tk.HIDDEN) # 隐藏果实按钮文本
            self.canvas.itemconfig(self.separator4, state=tk.HIDDEN) # 隐藏分隔线4
            self.canvas.itemconfig(self.log_button_rect, state=tk.HIDDEN) # 隐藏日志按钮
            self.canvas.itemconfig(self.log_text_id, state=tk.HIDDEN) # 隐藏日志按钮文本
            self.canvas.config(height=self.btn_height)
            self.geometry(f"{self.btn_width}x{self.btn_height}")  # 更新窗口高度

    def _configure_styles(self):
        font_family = "LXGW WenKai Mono"
        self.hex_label_font_size = int(12 * self.scale)
        self.hex_rank_font_size = int(self.hex_label_font_size * 1.6)
        hex_rank_font = ("Microsoft YaHei", self.hex_rank_font_size, "bold")
        self.hex_extra_info_font_size = int(self.hex_label_font_size * 1.1)
        hex_extra_info_font = (font_family, self.hex_extra_info_font_size, "bold")

        # 装备的字体 (缩小)
        self.equip_rank_font_size = int(12 * self.scale * 1.07)
        self.equip_data_font_size = int(12 * self.scale * 0.8) # 进一步缩小数据字体以确保能显示
        equip_rank_font = ("Microsoft YaHei", self.equip_rank_font_size, "bold")
        equip_data_font = ("LXGW WenKai Mono", self.equip_data_font_size, "bold")
        
        # [诊断代码] 打印最终计算出的字体信息
        if self.debug_mode:
            print(f"[样式诊断] 装备数据字体: {equip_data_font}")


        self.style = ttk.Style()
        self.style.theme_create("game_style", parent="alt", settings={
            "TFrame": {"configure": {"background": "systemTransparent"}},
            "HexRank.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "systemTransparent", "padding": "0 0"}},
            "HexRankS.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "#FF5C5C", "padding": "0 0"}},
            "HexRankA.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "#FFB83D", "padding": "0 0"}},
            "HexRankB.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "#FFF33F", "padding": "0 0"}},
            "HexRankC.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "#5CE1E6", "padding": "0 0"}},
            "HexRankD.TLabel": {"configure": {"font": hex_rank_font, "foreground": "#000000", "background": "#90EE90", "padding": "0 0"}},
            "EquipRank.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "systemTransparent", "padding": "0 0"}},
            "EquipRankS.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "#FF5C5C", "padding": "0 0"}},
            "EquipRankA.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "#FFB83D", "padding": "0 0"}},
            "EquipRankB.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "#FFF33F", "padding": "0 0"}},
            "EquipRankC.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "#5CE1E6", "padding": "0 0"}},
            "EquipRankD.TLabel": {"configure": {"font": equip_rank_font, "foreground": "#000000", "background": "#90EE90", "padding": "0 0"}},
            "EquipData.TLabel": {"configure": {"font": equip_data_font, "foreground": "#000000", "background": "systemTransparent", "padding": "0 0"}}
        })
        self.style.theme_use("game_style")

    def show_borderless_tip(self):
        """显示无边框提示弹窗，如果用户选择不再提醒则保存设置"""
        # 检查是否已选择不再提醒
        config_path = os.path.join(config.BASE_PATH, "config.json")
        show_tip = True
        
        # 修改配置读取逻辑，确保正确读取
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    app_config = json.load(f)
                    if 'show_borderless_tip' in app_config:
                        show_tip = app_config['show_borderless_tip']
            except Exception as e:
                print(f"读取配置文件出错: {e}")
                # 出错时默认显示提示
                show_tip = True
        
        if show_tip:
            # 创建自定义对话框
            tip_window = tk.Toplevel(self)
            tip_window.title("重要提示")
            
            # 使用缩放因子计算窗口大小，保持相对大小一致
            tip_width = int(450 * self.scale)
            tip_height = int(500 * self.scale)
            tip_window.geometry(f"{tip_width}x{tip_height}")
            
            tip_window.resizable(False, False)
            tip_window.attributes('-topmost', True)
            tip_window.focus_force()
            
            # 设置对话框内容
            frame = tk.Frame(tip_window, padx=int(20*self.scale), pady=int(20*self.scale))
            frame.pack(fill=tk.BOTH, expand=True)
            
            # 图标和文本组合
            icon_label = tk.Label(frame, text="⚠️", font=("Arial", int(28*self.scale)), fg="#FF9800")
            icon_label.pack(pady=(0, int(10*self.scale)))
            
            # 消息内容 - 使用红色文字
            msg = "务必将游戏窗口改为无边框！！！\n否则助手无法正常显示！！！\n\n进入游戏后，打开设置（ESC）→视频→窗口模式→选择无边框→确认\n\n点击按钮，自动显示对应评分，遮挡游戏内文字，可能会影响效果"
            message_label = tk.Label(frame, text=msg, justify=tk.LEFT, wraplength=int(400*self.scale), 
                                    fg="red", font=("Microsoft YaHei", int(11*self.scale), "bold"))
            message_label.pack(pady=int(10*self.scale))
            
            # 不再提醒选项 - 增强视觉效果
            dont_show_frame = tk.Frame(frame, pady=int(5*self.scale))
            dont_show_frame.pack(fill=tk.X, pady=int(5*self.scale))
            
            dont_show_var = tk.IntVar()
            dont_show_check = tk.Checkbutton(dont_show_frame, 
                                            text="不再提醒", 
                                            variable=dont_show_var,
                                            font=("Microsoft YaHei", int(10*self.scale)),
                                            fg="#0066CC",
                                            pady=int(5*self.scale))
            dont_show_check.pack(side=tk.LEFT)
            
            # 按钮区域
            btn_frame = tk.Frame(frame)
            btn_frame.pack(pady=(int(10*self.scale), 0), fill=tk.X)
            
            def on_ok():
                # 保存设置 - 修改配置保存逻辑，确保正确写入
                if dont_show_var.get() == 1:
                    # 先读取现有配置（如果有）
                    app_config = {}
                    if os.path.exists(config_path):
                        try:
                            with open(config_path, 'r', encoding='utf-8') as f:
                                app_config = json.load(f)
                        except Exception:
                            # 如果读取失败，使用空配置
                            app_config = {}
                    
                    # 更新配置并保存
                    app_config['show_borderless_tip'] = False
                    try:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(app_config, f, ensure_ascii=False, indent=2)
                        print("成功保存配置：不再显示提示")
                        # 显示设置已保存的提示
                        messagebox.showinfo("设置已保存", "已设置为不再显示此提示")
                    except Exception as e:
                        print(f"保存配置失败: {e}")
                        messagebox.showerror("保存失败", f"无法保存配置: {e}")
                
                tip_window.destroy()
            
            ok_btn = tk.Button(btn_frame, 
                              text="我已了解", 
                              command=on_ok, 
                              width=15, 
                              height=1,
                              bg="#4682B4",
                              fg="white",
                              font=("Microsoft YaHei", int(10*self.scale), "bold"))
            ok_btn.pack(pady=int(5*self.scale))
            
            # 对话框居中显示
            tip_window.update_idletasks()
            width = tip_window.winfo_width()
            height = tip_window.winfo_height()
            x = (self.winfo_screenwidth() // 2) - (width // 2)
            y = (self.winfo_screenheight() // 2) - (height // 2)
            tip_window.geometry(f'+{x}+{y}')
            
            # 模态对话框
            tip_window.transient(self)
            tip_window.grab_set()
            self.wait_window(tip_window)

    # --- [新增] 右键诊断模式功能 ---
    def setup_context_menu(self):
        """创建并绑定右键上下文菜单。"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(
            label="切换诊断模式 (Toggle Debug Mode)", 
            command=self.toggle_debug_mode_via_menu
        )
        # 将右键点击事件绑定到整个主窗口
        self.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """在鼠标右键点击的位置显示上下文菜单。"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def toggle_debug_mode_via_menu(self):
        """
        切换调试模式的开关。
        这个函数负责读写配置文件并给用户提示。
        """
        parser = configparser.ConfigParser()
        # 使用从config模块导入的路径，确保路径统一
        config_file = config.CONFIG_INI_PATH
        parser.read(config_file, encoding='utf-8')

        current_state = parser.getboolean('Settings', 'debug_mode', fallback=False)
        new_state = not current_state
        
        if not parser.has_section('Settings'):
            parser.add_section('Settings')
            
        parser.set('Settings', 'debug_mode', str(new_state))

        with open(config_file, 'w', encoding='utf-8') as f:
            parser.write(f)

        if new_state:
            messagebox.showinfo("诊断模式", "诊断模式已【开启】。\n\n请完全关闭并重新启动程序使设置生效。")
        else:
            messagebox.showinfo("诊断模式", "诊断模式已【关闭】。\n\n请完全关闭并重新启动程序使设置生效。")
    # --- 右键菜单功能结束 ---

    # 新增: 打开日志目录的方法
    def open_log_directory(self, event=None):
        """打开存放日志文件的文件夹"""
        # [修改] 使用新的 APP_PATH 来定位logs目录，确保路径在任何环境下都正确
        log_dir = os.path.join(config.APP_PATH, 'logs')
        # 确保目录存在，以防万一
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            messagebox.showinfo("提示", "日志目录已创建，但当前为空。")

        try:
            # os.startfile 是Windows特有的功能，可以直接用默认程序打开文件或目录
            os.startfile(os.path.abspath(log_dir))
        except AttributeError:
            # 兼容非Windows系统 (macOS, Linux)
            import subprocess
            if sys.platform == "darwin":  # macOS
                subprocess.Popen(["open", os.path.abspath(log_dir)])
            else:  # Linux
                subprocess.Popen(["xdg-open", os.path.abspath(log_dir)])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志目录: {e}")


    # 添加鼠标释放事件处理
    def on_main_button_release(self, event):
        if not self.drag_active:
            self.toggle_buttons(event)
        self.drag_active = False

    # 修改start_drag方法，添加拖拽状态跟踪
    def start_drag(self, event):
        self.drag_data["x"] = event.x_root
        self.drag_data["y"] = event.y_root
        self.drag_active = False  # 初始时不是拖动状态

    # 修改on_drag方法，添加拖拽状态判断
    def on_drag(self, event):
        # 计算移动距离，判断是否为拖拽
        dx = event.x_root - self.drag_data["x"]
        dy = event.y_root - self.drag_data["y"]
        
        # 如果移动距离超过阈值，设置为拖拽状态
        if abs(dx) > 5 or abs(dy) > 5:
            self.drag_active = True
        
        # 移动窗口
        x = self.winfo_x() + dx
        y = self.winfo_y() + dy
        self.geometry(f"+{x}+{y}")
        self.drag_data["x"] = event.x_root
        self.drag_data["y"] = event.y_root

    def show_round_status(self, event):
        """显示当前轮次状态"""
        current_round = self.round_manager.get_current_round() # 从管理器获取状态
        if current_round:
            messagebox.showinfo("轮次状态", f"当前检测到的轮次: {current_round}\\n目标轮次: {', '.join(config.TARGET_ROUNDS)}")
        else:
            messagebox.showinfo("轮次状态", f"当前未检测到轮次\\n目标轮次: {', '.join(config.TARGET_ROUNDS)}")
        
        if hasattr(self, 'hex_manager') and self.hex_manager.is_running():
            messagebox.showinfo("海克斯OCR状态", "海克斯OCR正在运行")

    def on_close(self, event=None):
        """关闭窗口时的快速清理操作"""
        try:
            logging.info("[App] 开始快速关闭流程...")
            
            # 第一步：立即停止所有OCR循环（最耗时的操作）
            try:
                if hasattr(self, 'hex_manager') and self.hex_manager.is_running():
                    self.hex_manager.hex_ocr_running = False
                    self.hex_manager.ocr_timer.stop_timer()
                    logging.info("[App] 海克斯OCR已停止")
            except Exception as e:
                logging.warning(f"[App] 停止海克斯OCR时出错: {e}")
            
            try:
                if hasattr(self, 'equip_manager') and self.equip_manager.is_running():
                    self.equip_manager.equip_ocr_running = False
                    self.equip_manager.ocr_timer.stop_timer()
                    logging.info("[App] 装备OCR已停止")
            except Exception as e:
                logging.warning(f"[App] 停止装备OCR时出错: {e}")
                
            try:
                if hasattr(self, 'trigger_manager') and self.trigger_manager.is_monitoring:
                    self.trigger_manager.stop_monitoring()
                    logging.info("[App] 触发管理器已停止")
            except Exception as e:
                logging.warning(f"[App] 停止触发管理器时出错: {e}")

            # 第二步：快速销毁窗口（同步执行，避免异步等待）
            self._fast_destroy_windows()
            
            # 第三步：立即释放端口锁（不等待atexit）
            try:
                import instance_checker
                global _lock_released
                # 获取全局的lock_socket并立即释放
                if hasattr(self, '_lock_socket') and self._lock_socket and not _lock_released:
                    instance_checker.release_lock(self._lock_socket)
                    self._lock_socket = None
                    _lock_released = True
                    logging.info("[App] 端口锁已释放")
            except Exception as e:
                logging.warning(f"[App] 立即释放端口锁失败: {e}")
            
            # 第四步：立即退出
            logging.info("[App] 快速关闭完成，程序退出")
            
        except Exception as e:
            # 即使出错也要确保程序能退出
            print(f"[App] 关闭过程中出现错误: {e}")
        
        finally:
            # 无论如何都要退出
            try:
                self.quit()  # 使用quit()而不是destroy()
            except:
                pass
            sys.exit(0)
    
    def _fast_destroy_windows(self):
        """快速同步销毁所有窗口，避免异步等待"""
        try:
            # 海克斯窗口快速销毁
            if hasattr(self, 'hex_manager'):
                for window in self.hex_manager.hex_overlay_windows:
                    try:
                        window.destroy()
                    except:
                        pass
                
                # 销毁变体和查询窗口
                for widget in self.hex_manager.hex_variant_widgets:
                    if widget:
                        try:
                            widget['window'].destroy()
                        except:
                            pass
                
                for widget in self.hex_manager.hex_query_widgets:
                    if widget:
                        try:
                            widget['window'].destroy()
                        except:
                            pass
                
                # 销毁调试框
                for box in self.hex_manager.hex_debug_boxes:
                    try:
                        box.destroy()
                    except:
                        pass
            
            # 装备窗口快速销毁
            if hasattr(self, 'equip_manager'):
                for window in self.equip_manager.equip_overlay_windows:
                    try:
                        window.destroy()
                    except:
                        pass
                        
        except Exception as e:
            logging.warning(f"[App] 快速销毁窗口时出现错误（可忽略）: {e}")
            # 即使出错也继续退出流程


def start_headless_mode(api_port=8888):
    """启动无头模式"""
    print(f"[HEADLESS] Starting headless mode, API port: {api_port}")

    try:
        # 初始化日志
        logging_system = logger_setup.setup_logging()
        logger = logging.getLogger('HeadlessMain')
        logger.info(f"无头模式启动，API端口: {api_port}")

        # 创建隐藏的TFTAssistantApp实例
        app = TFTAssistantApp(hidden=True)

        print(f"[SUCCESS] Hidden TFT Assistant started with API on port {api_port}")
        print("[INFO] Available API endpoints:")
        print("  GET  /api/status - Get status")
        print("  POST /api/scan_hex - Manual hex scan")
        print("  POST /api/scan_equipment - Manual equipment scan")
        print("  POST /api/scan_fruit - Manual fruit scan")
        print("  POST /api/toggle_auto - Toggle auto mode")
        print("  POST /api/show_window - Show window")
        print("  POST /api/hide_window - Hide window")
        print("\n[RUNNING] Headless mode running, press Ctrl+C to exit...")

        # 启动自动模式
        if app.trigger_manager:
            app.trigger_manager.start_monitoring()

        # 运行主循环
        app.mainloop()

    except KeyboardInterrupt:
        print("\n[STOP] Received exit signal, shutting down...")
        if 'app' in locals():
            if app.trigger_manager:
                app.trigger_manager.stop_monitoring()
            if app.api_server:
                app.api_server.shutdown()
        print("[EXIT] Headless mode exited")
    except Exception as e:
        print(f"[ERROR] Headless mode startup failed: {e}")
        logging.getLogger('HeadlessMain').error(f"无头模式启动失败: {e}")

def start_gui_mode():
    """启动GUI模式"""
    print("[GUI] Starting GUI mode...")

    try:
        # [修改] 启动顺序调整：主程序不再检查应用更新
        # 1. 检查程序是否已在运行，并获取锁对象
        lock_socket = instance_checker.check_and_lock()

        # 2. [关键] 使用atexit注册一个退出处理函数，确保程序退出时一定释放锁
        #    这是解决关闭后端口占用的核心。
        # 创建一个全局变量来跟踪锁是否已被释放
        global _lock_released
        _lock_released = False

        def safe_release_lock(socket_obj):
            global _lock_released
            if not _lock_released and socket_obj:
                instance_checker.release_lock(socket_obj)
                _lock_released = True

        if lock_socket:
            atexit.register(safe_release_lock, lock_socket)

        # 3. 在启动App前初始化日志
        logging_system = logger_setup.setup_logging()

        # 4. 记录模块加载信息
        logging_system.log_module_loading('config', config.__file__ if hasattr(config, '__file__') else None)
        logging_system.log_module_loading('utils', utils.__file__ if hasattr(utils, '__file__') else None)
        logging_system.log_module_loading('ocr_handler', ocr_handler.__file__ if hasattr(ocr_handler, '__file__') else None)
        logging_system.log_module_loading('data_query', data_query.__file__ if hasattr(data_query, '__file__') else None)
        logging_system.log_module_loading('managers', managers.__file__ if hasattr(managers, '__file__') else None)
        logging_system.log_module_loading('instance_checker', instance_checker.__file__ if hasattr(instance_checker, '__file__') else None)
        logging_system.log_module_loading('updater', updater.__file__ if hasattr(updater, '__file__') else None)

        # 5. 启动应用程序
        app = TFTAssistantApp(hidden=False)

        # 6. 记录应用程序初始化完成
        main_logger = logging.getLogger('MAIN')
        main_logger.info("TFTAssistantApp主窗口初始化完成")

        # 7. 将lock_socket保存到app实例中，以便快速释放
        app._lock_socket = lock_socket

        # 8. [新增] 在主循环开始前，检查一次数据库更新
        # 这样可以确保在主界面显示时，数据库已是最新
        main_logger.info("启动数据库更新检查线程")
        threading.Thread(target=updater.check_db_only, daemon=True).start()

        app.mainloop()

    except (EnvironmentError, FileNotFoundError):
        messagebox.showerror("环境错误", "文件路径中请勿包含中文!")
        sys.exit(1)
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='TFT弈秒决助手')
    parser.add_argument('--headless', action='store_true', help='无头模式运行（不显示GUI）')
    parser.add_argument('--api-port', type=int, default=8888, help='API服务器端口（默认8888）')
    args = parser.parse_args()

    # 添加全局异常处理
    def log_exception(exc_type, exc_value, exc_traceback):
        import traceback
        # [修改] 使用新的 APP_PATH 来定位error_log.txt
        log_file_path = os.path.join(config.APP_PATH, 'error_log.txt')
        with open(log_file_path, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))

    sys.excepthook = log_exception

    # 根据命令行参数选择启动模式
    if args.headless:
        start_headless_mode(args.api_port)
    else:
        start_gui_mode()