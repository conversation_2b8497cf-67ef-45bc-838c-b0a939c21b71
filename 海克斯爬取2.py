# pip install playwright
# playwright install

from playwright.sync_api import sync_playwright

def main():
    with sync_playwright() as p:
        # 1. 启动浏览器（headless=False 便于可视化调试，生产环境可改为 True）
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        # 2. 访问页面并等待主要内容加载完成
        page.goto("https://tftacademy.com/tierlist/augments", timeout=60000)
        page.wait_for_selector("div.relative")  # 等待主体区域出现

        # 3. 定位到“Augments / Comps / Items”按钮的父容器
        container = page.locator(
            "div.text-offwhite.tierlist\\:items-start "
            ">> div.tierlist\\:flex-row "
            ">> div.tierlist\\:text-sm "
            ">> div.hidden.lg\\:flex"
        )

        # 4. 计算按钮数量，理论上应为 3
        btn_count = container.locator("button").count()
        print(f"找到 {btn_count} 个切换按钮")

        # 5. 依次点击每个按钮，截图留证
        for idx in range(btn_count):
            btn = container.locator("button").nth(idx)
            label = btn.inner_text().strip() or f"button_{idx}"
            print(f"点击第 {idx+1} 个按钮 — 文本：{label}")
            btn.click()
            page.wait_for_timeout(2000)  # 等待切换动画或网络请求

            # 截图保存到当前目录，文件名包含按钮文本
            safe_label = label.replace(" ", "_").replace("/", "-")
            page.screenshot(path=f"augments_{safe_label}.png")

        # 6. 关闭浏览器
        browser.close()

if __name__ == "__main__":
    main()