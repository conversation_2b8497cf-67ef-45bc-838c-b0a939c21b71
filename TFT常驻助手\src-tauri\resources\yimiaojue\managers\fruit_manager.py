# -*- coding: utf-8 -*-
"""
果实识别功能的总管理器。

本文件包含用于管理果实识别相关的所有状态、UI元素和业务逻辑。
果实识别具有极低频率刷新的特点，大部分时间不刷新，如果刷新则三个一起刷新。
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import re
import imagehash
import sqlite3
import logging
from PIL import Image, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import utils
import ocr_handler
import data_query
from dynamic_ocr_timer import DynamicOCRTimer

class FruitManager:
    """
    果实识别功能的总管理器。
    封装了与果实识别相关的所有状态、UI元素和业务逻辑。
    """
    def __init__(self, app_ref):
        """
        初始化FruitManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
        """
        self.app = app_ref
        self.fruit_query = data_query.FruitQuery()
        self.fruit_whitelist = self._load_fruit_whitelist()

        # --- 状态变量 ---
        self.fruit_ocr_running = False
        self.fruit_start_time = 0
        self.fruit_no_detect_time = 0
        self.selected_hero = None  # 当前选择的英雄，用于显示英雄特定的果实数据
        
        # --- 动态时序控制器 ---
        self.ocr_timer = DynamicOCRTimer(
            initial_timeout=5,         # 自动触发5秒超时（果实刷新频率极低）
            manual_timeout=10,         # 手动触发10秒超时
            extension_timeout=5,       # 有效结果延长5秒
            window_disappear_delay=5   # 窗口消失后5秒延迟关闭
        )

        # --- [新增] 缓存的游戏窗口坐标和监测优化 ---
        self.cached_game_window_rect = None
        self.last_window_check_time = 0
        self.window_check_interval = 10.0  # 每10秒重新检测一次窗口坐标（果实检测频率低）

        # --- 新的状态管理机制 ---
        self.last_fruit_image_hashes = [None, None, None]  # 用于检测图像变化的哈希值
        self.pending_results = {}  # 存储待处理的OCR结果
        self.stable_results = [None, None, None]    # 用于存储稳定的识别结果

        # --- 图像与哈希相关 ---
        self.hash_threshold = 10.0 # 与海克斯保持一致的哈希阈值
        self.confidence_threshold = 70 # OCR识别成功的分数阈值，与海克斯保持一致
        self.last_fruit_image_hashes_lock = threading.Lock()

        # --- UI元素 ---
        self.fruit_overlay_windows = []
        self.fruit_rank_labels = []
        self.fruit_query_widgets = [None, None, None] 
        self.fruit_debug_boxes = [] # 用于显示OCR区域的调试红框
        self.fruit_extra_info_window_height = int(self.app.hex_extra_info_font_size * 2.2) # 复用海克斯的字体设置
        self.hero_search_window = None  # 英雄搜索窗口
        self._create_fruit_widgets() # 初始化时创建UI组件
        self._create_debug_boxes() # 初始化时创建调试红框

        # --- 结果跟踪 ---
        self.last_fruit_names = [None, None, None]
        self.last_fruit_ranks = [None, None, None]
        self.first_result_logged = False

        # --- 果实显示区域配置 (基于2560x1440分辨率的相对坐标) ---
        # 显示区域与OCR区域相同，直接使用config中的配置
        self.FRUIT_OVERLAY_REGIONS = config.FRUIT_SCREENSHOT_REGIONS_RELATIVE

    def _load_fruit_whitelist(self):
        """加载果实白名单，用于OCR识别的候选词汇。"""
        try:
            # 从数据库加载所有果实名称作为白名单
            conn = sqlite3.connect(config.HEX_DB_PATH)  # 复用海克斯数据库
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT powerup_name FROM powerups")
            whitelist = [row[0] for row in cursor.fetchall()]
            conn.close()
            logging.info(f"[Fruit] 加载果实白名单成功，共{len(whitelist)}个果实")
            return whitelist
        except Exception as e:
            logging.error(f"[Fruit] 加载果实白名单失败: {e}")
            return []

    def _create_fruit_widgets(self):
        """创建果实显示相关的UI组件。"""
        # 果实显示区域配置 (基于2560x1440分辨率的相对坐标)
        self.FRUIT_OVERLAY_REGIONS_RELATIVE = [
            (665/2560, 1153/1440, 821/2560, 1203/1440),   # 果实显示位置1
            (1128/2560, 1153/1440, 1284/2560, 1203/1440), # 果实显示位置2
            (1591/2560, 1153/1440, 1747/2560, 1203/1440)  # 果实显示位置3
        ]

        # 创建三个果实评级显示窗口
        for i in range(3):
            window = tk.Toplevel(self.app)
            window.withdraw()  # 初始隐藏
            window.overrideredirect(True)
            window.attributes('-topmost', True)
            try:
                window.configure(bg='systemTransparent')
            except tk.TclError:
                # 如果systemTransparent不支持，使用黑色背景
                window.configure(bg='black')
                window.attributes('-alpha', 0.8)  # 设置透明度
            
            # 创建评级标签
            rank_label = ttk.Label(window, text="", style="EquipRank.TLabel", anchor="center")  # 复用装备的样式
            rank_label.pack(fill=tk.BOTH, expand=True)
            
            self.fruit_overlay_windows.append(window)
            self.fruit_rank_labels.append(rank_label)

    def _create_debug_boxes(self):
        """创建调试用的红框，用于显示OCR识别区域。"""
        # 使用一种不太可能出现的颜色作为透明色
        TRANSPARENT_COLOR = '#abcdef'

        for i in range(3):
            debug_box = tk.Toplevel(self.app)
            debug_box.overrideredirect(True)
            debug_box.attributes('-topmost', True)
            # 设置透明色
            debug_box.attributes('-transparentcolor', TRANSPARENT_COLOR)
            # 设置窗口背景为透明色
            debug_box.config(bg=TRANSPARENT_COLOR)
            # 禁用窗口，让鼠标事件穿透
            debug_box.attributes('-disabled', 1)

            # 创建一个带红色边框的Frame来实现红框效果
            frame = tk.Frame(debug_box,
                             highlightbackground="red",
                             highlightcolor="red",
                             highlightthickness=2,
                             bg=TRANSPARENT_COLOR)
            frame.pack(expand=True, fill=tk.BOTH)

            self.fruit_debug_boxes.append(debug_box)
            debug_box.withdraw() # 初始状态为隐藏

    def _show_debug_boxes(self):
        """显示调试红框。"""
        self._update_debug_boxes_positions()  # 先更新位置
        for box in self.fruit_debug_boxes:
            box.deiconify()

    def _hide_debug_boxes(self):
        """隐藏调试红框。"""
        for box in self.fruit_debug_boxes:
            box.withdraw()

    def _update_debug_boxes_positions(self):
        """更新调试红框的位置。"""
        if not self.app.debug_mode or not self.fruit_debug_boxes:
            return

        game_rect = self._get_game_window_rect_cached()
        if not game_rect:
            return

        for i, debug_box in enumerate(self.fruit_debug_boxes):
            if i < len(config.FRUIT_SCREENSHOT_REGIONS_RELATIVE):
                region = config.FRUIT_SCREENSHOT_REGIONS_RELATIVE[i]
                bbox = utils.convert_relative_to_absolute(region, game_rect)
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]
                debug_box.geometry(f"{width}x{height}+{bbox[0]}+{bbox[1]}")
                if self.app.debug_mode:
                    print(f"[FruitManager] 调试框 {i} 位置更新: {width}x{height}+{bbox[0]}+{bbox[1]}")

    def _get_game_window_rect_cached(self):
        """获取缓存的游戏窗口坐标，定期更新以提高性能。"""
        current_time = time.time()
        if (self.cached_game_window_rect is None or 
            current_time - self.last_window_check_time > self.window_check_interval):
            
            self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
            self.last_window_check_time = current_time
            
            if self.app.debug_mode:
                logging.debug(f"[Fruit] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
        
        return self.cached_game_window_rect

    def is_running(self):
        """检查果实OCR是否正在运行。"""
        return self.fruit_ocr_running

    def start_fruit_ocr(self, event=None, manual=True):
        """
        启动果实OCR识别。

        Args:
            event: 事件对象（用于按钮绑定）
            manual (bool): 是否为手动触发
        """
        if self.fruit_ocr_running:
            if manual:
                # 确保弹窗显示在最顶层（仿照海克斯）
                if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                    temp_window = tk.Toplevel()
                    temp_window.withdraw()
                    temp_window.attributes('-topmost', True)
                    messagebox.showinfo("提示", "果实评分已在运行中。", parent=temp_window)
                    temp_window.destroy()
                else:
                    messagebox.showinfo("提示", "果实评分已在运行中。", parent=self.app)
            logging.info("[Fruit] 果实OCR已在运行，跳过重复启动")
            return "果实OCR已在运行"

        logging.info(f"启动果实OCR功能 (触发方式: {'手动' if manual else '自动'})。")
        if manual:
            # 确保弹窗显示在最顶层（仿照海克斯）
            if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                # 无头模式下创建临时顶层窗口来显示消息
                temp_window = tk.Toplevel()
                temp_window.withdraw()  # 隐藏窗口本身
                temp_window.attributes('-topmost', True)
                messagebox.showinfo("果实OCR", "果实评分已开启", parent=temp_window)
                temp_window.destroy()
            else:
                messagebox.showinfo("果实OCR", "果实评分已开启", parent=self.app)

        self.fruit_ocr_running = True
        self.fruit_start_time = time.time()
        self.fruit_no_detect_time = 0
        self.first_result_logged = False

        # 启动动态时序控制器
        self.ocr_timer.start_timer(manual=manual)

        # 如果在调试模式下，显示OCR区域红框
        if self.app.debug_mode:
            self._show_debug_boxes()

        # 自动显示英雄选择窗口（随评分同步出现）
        self.app.after(500, self.create_hero_search_window)  # 延迟500ms显示，确保OCR已启动

        # 启动OCR线程
        threading.Thread(target=self._fruit_ocr_loop, daemon=True).start()

        # 启动定期窗口检查机制（保险措施）
        self.app.after(3000, self._start_periodic_window_check)  # 3秒后开始检查

        trigger_type = "手动" if manual else "自动"
        logging.info(f"[Fruit] {trigger_type}启动果实OCR")
        return f"果实OCR已{trigger_type}启动"

    def stop_fruit_ocr(self):
        """停止果实OCR识别。"""
        if not self.fruit_ocr_running:
            return

        self.fruit_ocr_running = False
        self.ocr_timer.stop_timer()
        
        # 延迟隐藏窗口
        self.app.after(self.ocr_timer.window_disappear_delay * 1000, self.hide_all_windows)
        
        logging.info("[Fruit] 果实OCR已停止")

    def _fruit_ocr_loop(self):
        """果实OCR主循环。"""
        while self.fruit_ocr_running:
            try:
                # --- 快速响应停止信号 ---
                if not self.fruit_ocr_running:
                    logging.info("[Fruit] 检测到停止信号，立即退出循环")
                    break

                # --- 动态超时检测 ---
                if not self.ocr_timer.should_continue():
                    logging.info("[Fruit] 动态时序控制器指示停止，执行清理")
                    self.stop_fruit_ocr()
                    break

                game_rect = self._get_game_window_rect_cached()
                if not game_rect or game_rect[2] == 0:
                    time.sleep(0.5)
                    continue

                # 执行果实识别
                self._perform_fruit_recognition(game_rect)

                # 果实刷新频率极低，可以设置较长的循环间隔
                time.sleep(0.3)

            except Exception as e:
                logging.error(f"[Fruit] 果实OCR循环出错: {e}", exc_info=True)
                time.sleep(1)

        # 循环退出后确保清理资源
        if self.fruit_ocr_running:
            logging.info("[Fruit] OCR循环退出，执行最终清理")
            self.stop_fruit_ocr()

    def _perform_fruit_recognition(self, game_rect):
        """执行果实识别的核心逻辑。采用与海克斯相同的并行OCR策略。"""
        current_time = time.time()
        results = []

        # 步骤1: 批量截图和哈希检测，收集需要OCR的图像
        images_to_process = {}

        for i, region in enumerate(config.FRUIT_SCREENSHOT_REGIONS_RELATIVE):
            try:
                # 转换相对坐标为绝对坐标
                bbox = utils.convert_relative_to_absolute(region, game_rect)

                # 截图
                img = ocr_handler.capture_screen(bbox)
                if img is None:
                    continue

                # 计算图像哈希，检测变化
                current_hash = imagehash.phash(img)

                with self.last_fruit_image_hashes_lock:
                    last_hash = self.last_fruit_image_hashes[i]

                    # 如果图像没有显著变化，跳过OCR
                    if last_hash and abs(current_hash - last_hash) < self.hash_threshold:
                        results.append(self.stable_results[i])
                        continue

                    self.last_fruit_image_hashes[i] = current_hash

                # 将需要OCR的图像加入处理队列
                images_to_process[i] = img

            except Exception as e:
                logging.error(f"[Fruit] 果实区域{i+1}截图出错: {e}")
                results.append(None)

        # 步骤2: 并行OCR处理（专门为果实优化，跳过图像预处理）
        if images_to_process:
            # 使用专门为果实设计的OCR函数，跳过图像预处理
            image_list = list(images_to_process.values())
            index_list = list(images_to_process.keys())

            # 使用果实专用的OCR方法，跳过图像预处理，RapidOcr引擎内部已并行
            raw_text_parts = ocr_handler.ocr_recognize_fruit(image_list)

            # 步骤3: 对每个OCR结果进行智能查询和匹配
            for i, raw_text in enumerate(raw_text_parts):
                index = index_list[i]

                # 确保results列表长度足够
                while len(results) <= index:
                    results.append(None)

                if not raw_text:
                    if self.app.debug_mode:
                        print(f"[Fruit] 果实区域{index+1} OCR结果: 无文本识别")
                    results[index] = None
                    continue

                # 提取文本
                text = raw_text.strip()

                # 调试输出OCR结果
                if self.app.debug_mode:
                    print(f"[Fruit] 果实区域{index+1} OCR结果: '{text}'")

                # 如果文本为空，跳过
                if not text:
                    if self.app.debug_mode:
                        print(f"[Fruit] 果实区域{index+1} 文本为空，跳过")
                    results[index] = None
                    continue

                # 使用果实查询器进行匹配
                matched_candidates = self.fruit_query.fuzzy_match_whitelist(text, self.fruit_whitelist)
                if matched_candidates:
                    best_match = matched_candidates[0]  # 获取最佳匹配
                    match_name = best_match['name']
                    match_score = best_match['score']

                    # 应用置信度阈值检查
                    if match_score >= self.confidence_threshold:
                        fruit_data = self.fruit_query.smart_query(match_name, self.selected_hero)
                        if self.app.debug_mode:
                            fruit_name = fruit_data.get('data', {}).get('name', '未知') if fruit_data else '未知'
                            fruit_rank = fruit_data.get('data', {}).get('rank', '未知') if fruit_data else '未知'
                            print(f"[Fruit] 果实区域{index+1} 匹配成功: '{text}' -> {match_name} -> {fruit_name} (评级: {fruit_rank}, 置信度: {match_score:.1f})")
                        results[index] = fruit_data
                        self.stable_results[index] = fruit_data

                        # 通知动态时序控制器检测到有效结果（参照海克斯）
                        self.ocr_timer.extend_timer()
                    else:
                        if self.app.debug_mode:
                            print(f"[Fruit] 果实区域{index+1} 置信度不足: '{text}' -> {match_name} (置信度: {match_score:.1f} < {self.confidence_threshold})")
                        results[index] = None
                else:
                    if self.app.debug_mode:
                        print(f"[Fruit] 果实区域{index+1} 匹配失败: '{text}' 未找到对应果实")
                    results[index] = None

        # 确保results列表长度为3
        while len(results) < 3:
            results.append(None)

        # 更新显示
        self._update_fruit_display(results)

    def _update_fruit_display(self, results):
        """更新果实显示界面。"""
        valid_results = [r for r in results if r and r.get('status') == 'exact']
        successful_recognitions = len(valid_results)

        # 检查窗口可见状态并通知时序控制器
        windows_visible = self._check_windows_visibility()
        self.ocr_timer.set_windows_visible(windows_visible)

        if successful_recognitions >= 2:  # 至少识别到2个果实才显示，与海克斯保持一致
            # 当至少有两个识别成功时，我们认为进入了果实选择界面
            self.ocr_timer.extend_timer()  # 延长计时器

            if self.app.debug_mode:
                print(f"[Fruit] 识别成功数量: {successful_recognitions}/3，显示果实评级")

            # 显示所有位置，包括占位符
            for i, result in enumerate(results):
                if result and result.get('status') == 'exact':
                    self._show_fruit_result(i, result['data'])
                else:
                    self._show_fruit_placeholder(i)  # 显示占位符

            # 显示英雄选择窗口（随评分同步出现）
            if self.hero_search_window:
                self.hero_search_window.deiconify()
        else:
            # 当识别成功的数量少于2个时，隐藏所有窗口
            if self.app.debug_mode:
                print(f"[Fruit] 识别成功数量: {successful_recognitions}/3，不足2个，隐藏所有窗口")

            self.hide_all_windows()
            # 清除所有缓存，为下一次干净的识别做准备
            self.stable_results = [None, None, None]

    def _check_windows_visibility(self):
        """
        检查是否有果实窗口当前可见

        Returns:
            bool: True表示有窗口可见，False表示所有窗口都不可见
        """
        try:
            # 检查果实评级窗口
            for window in self.fruit_overlay_windows:
                if window.winfo_exists() and window.winfo_ismapped():
                    return True

            # 检查英雄选择窗口
            if (self.hero_search_window and
                self.hero_search_window.winfo_exists() and
                self.hero_search_window.winfo_ismapped()):
                return True

            return False

        except Exception as e:
            if self.app.debug_mode:
                logging.debug(f"[Fruit] 检查窗口可见性时出错: {e}")
            return False

    def _show_fruit_result(self, index, fruit_data):
        """显示单个果实的识别结果。"""
        if index >= len(self.fruit_overlay_windows):
            return
            
        window = self.fruit_overlay_windows[index]
        rank_label = self.fruit_rank_labels[index]
        
        # 获取果实信息
        fruit_name = fruit_data.get('name', '未知')
        fruit_rank = fruit_data.get('rank', 'Unknown')
        
        # 设置标签样式
        style_name = f"EquipRank{fruit_rank}.TLabel" if fruit_rank in ['S', 'A', 'B', 'C', 'D'] else "EquipRank.TLabel"
        rank_label.configure(style=style_name, text=fruit_rank)
        
        # 计算窗口位置
        game_rect = self._get_game_window_rect_cached()
        if game_rect:
            region = self.FRUIT_OVERLAY_REGIONS_RELATIVE[index]
            bbox = utils.convert_relative_to_absolute(region, game_rect)
            
            # 设置窗口位置和大小
            window.geometry(f"{bbox[2]-bbox[0]}x{bbox[3]-bbox[1]}+{bbox[0]}+{bbox[1]}")
            window.deiconify()

    def _show_fruit_placeholder(self, index):
        """显示果实占位符。"""
        if index >= len(self.fruit_overlay_windows):
            return

        window = self.fruit_overlay_windows[index]
        rank_label = self.fruit_rank_labels[index]

        # 设置占位符样式
        rank_label.configure(style="EquipRank.TLabel", text="?")

        # 计算窗口位置
        game_rect = self._get_game_window_rect_cached()
        if game_rect:
            region = self.FRUIT_OVERLAY_REGIONS_RELATIVE[index]
            bbox = utils.convert_relative_to_absolute(region, game_rect)

            # 设置窗口位置和大小
            window.geometry(f"{bbox[2]-bbox[0]}x{bbox[3]-bbox[1]}+{bbox[0]}+{bbox[1]}")
            window.deiconify()

    def _hide_fruit_result(self, index):
        """隐藏单个果实的显示窗口。"""
        if index < len(self.fruit_overlay_windows):
            self.fruit_overlay_windows[index].withdraw()

    def hide_all_windows(self):
        """隐藏所有果实显示窗口，包括英雄选择窗口。"""
        for window in self.fruit_overlay_windows:
            window.withdraw()

        for widget in self.fruit_query_widgets:
            if widget:
                widget['window'].withdraw()

        # 隐藏英雄选择窗口（随评分同步消失）
        self.hide_hero_search_window()

        # 通知动态时序控制器窗口不可见
        self.ocr_timer.set_windows_visible(False)

    def create_hero_search_window(self):
        """创建英雄搜索窗口，参照海克斯搜索框样式，支持拼音搜索。"""
        if self.hero_search_window:
            self.hero_search_window.lift()
            return

        # 计算窗口位置：在第一个果实窗口上方
        game_rect = self._get_game_window_rect_cached()
        if not game_rect:
            return  # 如果无法获取游戏窗口位置，不显示窗口

        # 获取第一个果实显示区域的位置
        first_fruit_region = self.FRUIT_OVERLAY_REGIONS_RELATIVE[0]
        fruit_bbox = utils.convert_relative_to_absolute(first_fruit_region, game_rect)

        # 创建窗口（参照海克斯样式）
        self.hero_search_window = tk.Toplevel(self.app)
        self.hero_search_window.overrideredirect(True)  # 无边框，无关闭按钮
        self.hero_search_window.attributes('-topmost', True)

        # UI布局（参照海克斯样式）
        main_frame = ttk.Frame(self.hero_search_window, style="Card.TFrame")
        main_frame.pack(expand=True, fill="both")

        title_label = ttk.Label(main_frame, text="选择英雄 (输入名称/拼音)", style="QueryTitle.TLabel")
        title_label.pack(pady=(5,2))

        entry_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=entry_var)
        entry.pack(fill='x', padx=5)

        listbox = tk.Listbox(main_frame, height=8, background="#333", foreground="#CCC",
                           selectbackground="#0078D7", relief="flat", borderwidth=0, highlightthickness=0)
        listbox.pack(expand=True, fill='both', padx=5, pady=5)

        # 调整窗口大小和位置
        window_width = fruit_bbox[2] - fruit_bbox[0]  # 与果实窗口同宽
        window_height = int(window_width * 1.2)  # 高度为宽度的1.2倍
        window_x = fruit_bbox[0]  # 与第一个果实窗口左对齐
        window_y = fruit_bbox[1] - window_height - 10  # 在果实窗口上方10像素

        # 确保窗口不会超出屏幕边界
        if window_y < 0:
            window_y = fruit_bbox[3] + 10  # 如果上方空间不够，显示在下方

        self.hero_search_window.geometry(f"{window_width}x{window_height}+{window_x}+{window_y}")

        # 初始化英雄列表
        heroes = self.fruit_query.get_heroes_with_powerups()
        for hero in heroes:
            listbox.insert(tk.END, hero)

        # 绑定事件
        def on_hero_search_keyup(event):
            """处理英雄搜索输入框的键盘抬起事件，实时更新建议列表。"""
            query = entry_var.get()
            # 调用模糊搜索算法
            results = self.fruit_query.fuzzy_search_heroes(query)

            listbox.delete(0, tk.END) # 清空列表

            # 显示搜索结果
            for hero in results:
                listbox.insert(tk.END, hero)

        def on_hero_select(event):
            """处理英雄选择事件。"""
            selection = listbox.curselection()
            if selection:
                selected_hero = listbox.get(selection[0])
                self.selected_hero = selected_hero
                logging.info(f"[Fruit] 选择英雄: {self.selected_hero}")

                # 隐藏英雄选择窗口
                self.hero_search_window.withdraw()

                # 立即重新调用一次OCR来刷新当前的评分
                self._trigger_immediate_ocr_refresh()

        entry.bind("<KeyRelease>", on_hero_search_keyup)
        listbox.bind("<<ListboxSelect>>", on_hero_select)
        listbox.bind("<Return>", on_hero_select)

        # 失去焦点时隐藏窗口（但不销毁，以便重复使用）
        self.hero_search_window.bind("<FocusOut>", lambda e: self.hero_search_window.withdraw())

        self.hero_search_window.deiconify()
        entry.focus_set()

    def _trigger_immediate_ocr_refresh(self):
        """选中英雄后立即触发一次OCR刷新，更新评分显示。"""
        if not self.fruit_ocr_running:
            return

        # 清除图像哈希缓存，强制重新OCR
        with self.last_fruit_image_hashes_lock:
            self.last_fruit_image_hashes = [None, None, None]

        # 清除稳定结果，强制重新计算
        self.stable_results = [None, None, None]

        logging.info("[Fruit] 英雄选择后触发OCR刷新")

    def hide_hero_search_window(self):
        """隐藏英雄搜索窗口。"""
        if self.hero_search_window:
            self.hero_search_window.withdraw()

    def _start_periodic_window_check(self):
        """启动定期窗口检查机制（保险措施）。"""
        if self.fruit_ocr_running:
            self._periodic_window_check()
            # 每5秒检查一次
            self.app.after(5000, self._start_periodic_window_check)

    def _periodic_window_check(self):
        """定期检查窗口状态，确保没有评分时窗口正确关闭。"""
        try:
            if not self.fruit_ocr_running:
                return

            # 检查是否有有效的评分结果
            has_valid_results = False
            if hasattr(self, 'stable_results') and self.stable_results:
                valid_results = [r for r in self.stable_results if r and r.get('status') == 'exact']
                has_valid_results = len(valid_results) >= 2

            # 检查窗口是否可见
            windows_visible = self._check_windows_visibility()

            # 如果窗口可见但没有有效评分，强制隐藏窗口
            if windows_visible and not has_valid_results:
                logging.warning("[Fruit] 定期检查发现窗口可见但无有效评分，强制隐藏窗口")
                self.hide_all_windows()

        except Exception as e:
            logging.error(f"[Fruit] 定期窗口检查出错: {e}", exc_info=True)
