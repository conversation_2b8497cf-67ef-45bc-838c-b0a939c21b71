<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
        }

        .floating-container {
            padding: 8px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            cursor: move;
            -webkit-app-region: drag;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        .status-section {
            margin-bottom: 12px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .status-icon {
            font-size: 16px;
        }

        .status-icon.running {
            color: #4ade80; /* 绿色 - 自动监测 */
        }

        .status-icon.manual {
            color: #fbbf24; /* 黄色 - 手动模式 */
        }

        .status-icon.stopped {
            color: #ef4444; /* 红色 - 未启动 */
        }

        .status-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            flex: 1;
        }

        .status-message {
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .controls-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            -webkit-app-region: no-drag;
        }

        .control-btn {
            padding: 8px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 36px;
        }

        .control-btn:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .control-btn.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            margin-top: 8px;
        }

        .loading.show {
            display: flex;
        }

        .spinner {
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="floating-container">
        <div class="status-section">
            <div class="status-indicator">
                <span class="status-icon" id="statusIcon">🔴</span>
                <span class="status-text" id="statusText">评分未启动</span>
            </div>
            <div class="status-message" id="statusMessage">正在初始化...</div>
        </div>

        <div class="controls-section">
            <button class="control-btn" id="toggleAutoBtn" onclick="toggleAutoMode()" disabled title="切换自动监测">
                <span id="autoIcon">▶️</span>
            </button>
            <button class="control-btn" id="scanHexBtn" onclick="manualScanHex()" disabled title="扫描海克斯">
                🔮
            </button>
            <button class="control-btn" id="scanEquipBtn" onclick="manualScanEquipment()" disabled title="扫描装备">
                ⚔️
            </button>
            <button class="control-btn" id="scanFruitBtn" onclick="manualScanFruit()" disabled title="扫描果实">
                🍎
            </button>
        </div>

        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <span class="loading-text" id="loadingText">处理中...</span>
        </div>
    </div>

    <script>
        // 等待Tauri API加载
        let invoke, listen;

        // 初始化Tauri API
        async function initTauriAPI() {
            if (window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.event) {
                invoke = window.__TAURI__.core.invoke;
                listen = window.__TAURI__.event.listen;
                console.log('Tauri API初始化成功');
                return true;
            }
            console.log('Tauri API不可用');
            return false;
        }

        // 状态变量
        let pythonRunning = false;
        let autoMode = false;
        let isLoading = false;
        let statusRefreshInterval = null;

        // DOM元素 - 将在DOM加载完成后初始化
        let statusIcon, statusText, statusMessage, toggleAutoBtn, autoIcon, scanHexBtn, scanEquipBtn, scanFruitBtn, loadingIndicator, loadingText;

        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('悬浮窗OCR控制面板已加载');

            // 初始化DOM元素
            statusIcon = document.getElementById('statusIcon');
            statusText = document.getElementById('statusText');
            statusMessage = document.getElementById('statusMessage');
            toggleAutoBtn = document.getElementById('toggleAutoBtn');
            autoIcon = document.getElementById('autoIcon');
            scanHexBtn = document.getElementById('scanHexBtn');
            scanEquipBtn = document.getElementById('scanEquipBtn');
            scanFruitBtn = document.getElementById('scanFruitBtn');
            loadingIndicator = document.getElementById('loadingIndicator');
            loadingText = document.getElementById('loadingText');

            // 等待Tauri API加载
            let retries = 0;
            while (!await initTauriAPI() && retries < 10) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }

            if (!invoke || !listen) {
                console.error('Tauri API加载失败');
                return;
            }

            await initializeOCR();
            setupEventListeners();

            // 监听窗口关闭事件
            window.addEventListener('beforeunload', () => {
                if (statusRefreshInterval) {
                    clearInterval(statusRefreshInterval);
                    statusRefreshInterval = null;
                    console.log('已停止状态刷新');
                }
            });

            // 定期刷新状态（每15秒，错开主窗口查询时间）
            statusRefreshInterval = setInterval(async () => {
                try {
                    const ocrStatus = await invoke('get_ocr_status');
                    if (ocrStatus.success && ocrStatus.data) {
                        const ocrData = JSON.parse(ocrStatus.data);
                        const oldAutoMode = autoMode;
                        const oldPythonRunning = pythonRunning;

                        pythonRunning = true; // 如果能获取到状态说明识别程序在运行
                        autoMode = ocrData.auto_mode;

                        // 只有状态改变时才更新UI和输出日志
                        if (oldAutoMode !== autoMode || oldPythonRunning !== pythonRunning) {
                            console.log('🔄 悬浮窗状态变化:', {
                                from: oldPythonRunning ? (oldAutoMode ? '自动' : '手动') : '停止',
                                to: pythonRunning ? (autoMode ? '自动' : '手动') : '停止'
                            });
                            updateUI();
                            // 通知主窗口状态变化
                            notifyMainWindow();
                        }
                    }
                } catch (error) {
                    // 静默处理错误，避免控制台spam
                    if (pythonRunning) {
                        pythonRunning = false;
                        updateUI();
                        notifyMainWindow();
                    }
                }
            }, 2000); // 改为2秒，与主窗口保持一致的高频率
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 监听主窗口的状态更新事件
            listen('ocr-status-update', (event) => {
                console.log('收到OCR状态更新:', event.payload);
                updateUIFromPayload(event.payload);
            });
        }

        // 更新UI状态
        function updateUI() {
            // 更新状态图标和文本
            if (!pythonRunning) {
                statusIcon.textContent = '🔴';
                statusText.textContent = '评分未启动';
            } else {
                statusIcon.textContent = autoMode ? '🟢' : '🟡';
                statusText.textContent = autoMode ? '评分自动监测' : '评分手动模式';
            }

            // 更新按钮状态
            const buttonsEnabled = pythonRunning && !isLoading;
            toggleAutoBtn.disabled = !buttonsEnabled;
            scanHexBtn.disabled = !buttonsEnabled;
            scanEquipBtn.disabled = !buttonsEnabled;
            scanFruitBtn.disabled = !buttonsEnabled;

            // 更新自动模式按钮
            if (autoMode) {
                toggleAutoBtn.classList.add('active');
                autoIcon.textContent = '🛑';
                toggleAutoBtn.title = '停止自动监测';
            } else {
                toggleAutoBtn.classList.remove('active');
                autoIcon.textContent = '▶️';
                toggleAutoBtn.title = '启动自动监测';
            }

            // 更新加载状态
            if (isLoading) {
                loadingIndicator.classList.add('show');
            } else {
                loadingIndicator.classList.remove('show');
            }
        }

        // 从事件载荷更新UI
        function updateUIFromPayload(payload) {
            if (payload.pythonRunning !== undefined) {
                pythonRunning = payload.pythonRunning;
            }
            if (payload.autoMode !== undefined) {
                autoMode = payload.autoMode;
            }
            if (payload.isLoading !== undefined) {
                isLoading = payload.isLoading;
            }
            if (payload.statusMessage !== undefined) {
                statusMessage.textContent = payload.statusMessage;
            }
            if (payload.loadingText !== undefined) {
                loadingText.textContent = payload.loadingText;
            }
            updateUI();
        }

        // 设置加载状态
        function setLoading(loading, message = '处理中...') {
            isLoading = loading;
            if (message) {
                loadingText.textContent = message;
            }
            updateUI();
        }

        // 设置状态消息
        function setStatusMessage(message) {
            statusMessage.textContent = message;
        }

        // 更新UI状态
        function updateUI() {
            console.log('更新UI状态:', { pythonRunning, autoMode });

            // 更新状态指示器
            if (pythonRunning) {
                if (autoMode) {
                    // 自动监测模式 - 绿色
                    if (statusIcon) {
                        statusIcon.className = 'status-icon running';
                        statusIcon.textContent = '●';
                    }
                    if (statusText) statusText.textContent = '评分自动监测';
                } else {
                    // 手动模式 - 黄色
                    if (statusIcon) {
                        statusIcon.className = 'status-icon manual';
                        statusIcon.textContent = '●';
                    }
                    if (statusText) statusText.textContent = '评分手动模式';
                }

                // 更新自动模式按钮
                if (autoIcon) {
                    autoIcon.textContent = autoMode ? '🔄' : '▶️';
                }

                // 启用按钮
                if (toggleAutoBtn) toggleAutoBtn.disabled = false;
                if (scanHexBtn) scanHexBtn.disabled = false;
                if (scanEquipBtn) scanEquipBtn.disabled = false;
                if (scanFruitBtn) scanFruitBtn.disabled = false;

                console.log('UI已更新为运行状态，自动模式:', autoMode);
            } else {
                // 停止状态 - 红色
                if (statusIcon) {
                    statusIcon.className = 'status-icon stopped';
                    statusIcon.textContent = '●';
                }
                if (statusText) statusText.textContent = '评分未启动';

                // 禁用按钮
                if (toggleAutoBtn) toggleAutoBtn.disabled = true;
                if (scanHexBtn) scanHexBtn.disabled = true;
                if (scanEquipBtn) scanEquipBtn.disabled = true;
                if (scanFruitBtn) scanFruitBtn.disabled = true;

                console.log('UI已更新为停止状态');
            }
        }

        // 初始化识别系统
        async function initializeOCR() {
            try {
                setLoading(true, '初始化识别系统...');
                setStatusMessage('正在初始化...');

                // 检查识别程序状态
                const pythonStatus = await invoke('check_yimiaojue_status');
                console.log('识别程序状态:', pythonStatus);

                if (pythonStatus.success && pythonStatus.data) {
                    const statusData = JSON.parse(pythonStatus.data);
                    pythonRunning = statusData.running;
                    console.log('识别程序运行状态:', pythonRunning);

                    if (pythonRunning) {
                        // 获取识别状态
                        const ocrStatus = await invoke('get_ocr_status');
                        console.log('识别状态:', ocrStatus);

                        if (ocrStatus.success && ocrStatus.data) {
                            const ocrData = JSON.parse(ocrStatus.data);
                            autoMode = ocrData.auto_mode;
                            console.log('自动模式状态:', autoMode);
                            updateUI();
                            setStatusMessage('识别系统就绪');
                        } else {
                            console.error('获取识别状态失败:', ocrStatus);
                            // 即使获取状态失败，如果程序在运行，也设置为手动模式
                            autoMode = false;
                            updateUI();
                            setStatusMessage('识别程序运行中');
                        }
                    } else {
                        console.log('识别程序未运行');
                        updateUI();
                        setStatusMessage('识别程序未启动');
                    }
                } else {
                    console.error('检查识别程序状态失败:', pythonStatus);
                    // 尝试直接获取OCR状态
                    try {
                        const ocrStatus = await invoke('get_ocr_status');
                        if (ocrStatus.success && ocrStatus.data) {
                            const ocrData = JSON.parse(ocrStatus.data);
                            pythonRunning = true;
                            autoMode = ocrData.auto_mode;
                            updateUI();
                            setStatusMessage('识别系统就绪');
                        } else {
                            updateUI();
                            setStatusMessage('识别程序未启动');
                        }
                    } catch (fallbackError) {
                        updateUI();
                        setStatusMessage('识别程序未启动');
                    }
                }

                setLoading(false);
            } catch (error) {
                console.error('初始化识别系统失败:', error);
                updateUI();
                setStatusMessage('识别程序未启动');
                setLoading(false);
            }
        }

        // 切换自动模式
        async function toggleAutoMode() {
            if (!pythonRunning || isLoading) return;
            
            try {
                setLoading(true, '切换监测模式...');
                const result = await invoke('toggle_auto_mode');
                
                if (result.success && result.data) {
                    const responseData = JSON.parse(result.data);
                    autoMode = responseData.auto_mode;
                    updateUI(); // 更新UI状态
                    notifyMainWindow(); // 通知主窗口状态变化
                    setStatusMessage(`自动监测已${responseData.action === 'started' ? '启动' : '停止'}`);
                } else {
                    setStatusMessage('切换监测模式失败');
                }
            } catch (error) {
                console.error('切换自动模式失败:', error);
                setStatusMessage('切换监测模式失败');
            } finally {
                setLoading(false);
            }
        }

        // 手动扫描海克斯
        async function manualScanHex() {
            if (!pythonRunning || isLoading) return;
            
            try {
                setLoading(true, '扫描海克斯...');
                const result = await invoke('manual_scan_hex');
                
                if (result.success) {
                    setStatusMessage('海克斯扫描完成');
                } else {
                    setStatusMessage('海克斯扫描失败');
                }
            } catch (error) {
                console.error('手动扫描海克斯失败:', error);
                setStatusMessage('海克斯扫描失败');
            } finally {
                setLoading(false);
            }
        }

        // 手动扫描装备
        async function manualScanEquipment() {
            if (!pythonRunning || isLoading) return;

            try {
                setLoading(true, '扫描装备...');
                const result = await invoke('manual_scan_equipment');

                if (result.success) {
                    setStatusMessage('装备扫描完成');
                } else {
                    setStatusMessage('装备扫描失败');
                }
            } catch (error) {
                console.error('手动扫描装备失败:', error);
                setStatusMessage('装备扫描失败');
            } finally {
                setLoading(false);
            }
        }

        // 手动扫描果实
        async function manualScanFruit() {
            if (!pythonRunning || isLoading) return;

            try {
                setLoading(true, '扫描果实...');
                const result = await invoke('manual_scan_fruit');

                if (result.success) {
                    setStatusMessage('果实扫描完成');
                } else {
                    setStatusMessage('果实扫描失败');
                }
            } catch (error) {
                console.error('手动扫描果实失败:', error);
                setStatusMessage('果实扫描失败');
            } finally {
                setLoading(false);
            }
        }



        // 通知主窗口状态变化
        async function notifyMainWindow() {
            try {
                if (window.__TAURI__ && window.__TAURI__.event) {
                    const { emit } = window.__TAURI__.event;
                    await emit('floating-ocr-status-update', {
                        pythonRunning,
                        autoMode,
                        isLoading
                    });
                    console.log('已通知主窗口状态变化:', { pythonRunning, autoMode, isLoading });
                }
            } catch (error) {
                console.error('通知主窗口失败:', error);
            }
        }

        // 关闭窗口
        async function closeWindow() {
            try {
                // 清理定时器
                if (statusRefreshInterval) {
                    clearInterval(statusRefreshInterval);
                    statusRefreshInterval = null;
                }

                // 使用invoke调用后端命令隐藏窗口
                await invoke('hide_floating_ocr_window');
                console.log('悬浮窗已隐藏');
            } catch (error) {
                console.error('关闭悬浮窗失败:', error);
                // 备用方案：直接关闭当前窗口
                try {
                    if (window.__TAURI__ && window.__TAURI__.webviewWindow) {
                        const { getCurrentWebviewWindow } = window.__TAURI__.webviewWindow;
                        const currentWindow = getCurrentWebviewWindow();
                        await currentWindow.hide();
                    }
                } catch (fallbackError) {
                    console.error('备用关闭方案也失败:', fallbackError);
                }
            }
        }
    </script>
</body>
</html>
