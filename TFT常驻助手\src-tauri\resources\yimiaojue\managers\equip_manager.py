# -*- coding: utf-8 -*-
"""
业务逻辑管理器模块

本文件包含用于管理特定业务逻辑的"经理"类。
每个管理器类封装一个独立功能模块的所有逻辑，包括状态管理、后台循环、UI交互等。
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import re
import imagehash
import sqlite3
import logging # 引入日志模块
from PIL import Image, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import utils
import ocr_handler
import data_query
from dynamic_ocr_timer import DynamicOCRTimer

class EquipManager:
    """
    装备识别功能的总管理器。
    封装了与装备识别相关的所有状态、UI元素和业务逻辑。
    """
    def __init__(self, app_ref):
        """
        初始化EquipManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
        """
        self.app = app_ref
        self.equip_query = data_query.EquipmentQuery()
        self.equip_whitelist = self._load_equip_whitelist()

        # --- 状态变量 ---
        self.equip_ocr_running = False
        self.equip_start_time = 0
        self.equip_no_detect_time = 0
        
        # --- 动态时序控制器 ---
        self.ocr_timer = DynamicOCRTimer(
            initial_timeout=5,         # 自动触发5秒超时
            manual_timeout=10,         # 手动触发10秒超时
            extension_timeout=5,       # 有效结果延长5秒
            window_disappear_delay=5   # 窗口消失后5秒延迟关闭
        )

        # --- [新增] 缓存的游戏窗口坐标和监测优化 ---
        self.cached_game_window_rect = None
        self.last_window_check_time = 0
        self.window_check_interval = 15.0  # 装备检测频率较低，可以设置更长的间隔

        # --- [新增] 状态跟踪，用于即时响应变化 ---
        self.last_stable_equip_count = 0
        self.last_logged_equip_count = -1 # 用于控制日志打印频率
        self.first_result_logged = False

        # --- 图像哈希相关 ---
        self.equip_last_image_hash = None
        self.hash_threshold = 15

        # --- UI元素 ---
        self.equip_overlay_windows = []

    def _load_equip_whitelist(self):
        """从数据库加载装备白名单。"""
        try:
            conn = sqlite3.connect(config.EQUIP_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM items")
            whitelist = [row[0] for row in cursor.fetchall()]
            conn.close()
            return whitelist
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"无法从数据库加载装备名称列表：{e}")
            return []

    def start_equip_ocr(self, event=None, manual=True):
        """
        启动装备OCR。
        Args:
            manual (bool): True表示由用户手动点击触发，False表示由后台自动触发。
        """
        if self.equip_ocr_running:
            if manual:
                # 确保弹窗显示在最顶层
                if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                    temp_window = tk.Toplevel()
                    temp_window.withdraw()
                    temp_window.attributes('-topmost', True)
                    messagebox.showinfo("提示", "装备评分已在运行中。", parent=temp_window)
                    temp_window.destroy()
                else:
                    messagebox.showinfo("提示", "装备评分已在运行中。", parent=self.app)
            return # 如果已经在运行，则不重复启动

        logging.info(f"启动装备OCR功能 (触发方式: {'手动' if manual else '自动'})。")
        if manual:
            # 确保弹窗显示在最顶层
            if hasattr(self.app, 'hidden_mode') and self.app.hidden_mode:
                # 无头模式下创建临时顶层窗口来显示消息
                temp_window = tk.Toplevel()
                temp_window.withdraw()  # 隐藏窗口本身
                temp_window.attributes('-topmost', True)
                messagebox.showinfo("装备OCR", "装备评分已开启", parent=temp_window)
                temp_window.destroy()
            else:
                messagebox.showinfo("装备OCR", "装备评分已开启", parent=self.app)

        self._get_game_window_rect_cached(force_update=True)
        
        self.equip_ocr_running = True
        self.equip_start_time = time.time()
        self.first_result_logged = False # 重置计时标志
        
        # 启动动态时序控制器
        self.ocr_timer.start_timer(manual=manual)
        
        threading.Thread(target=self.run_equip_ocr_loop, daemon=True).start()

        # 启动定期窗口检查机制（保险措施）
        self.app.after(3000, self._start_periodic_window_check)  # 3秒后开始检查

    def stop_equip_ocr(self):
        """停止装备OCR循环"""
        self.equip_ocr_running = False
        
        # 停止动态时序控制器
        self.ocr_timer.stop_timer()
        
        logging.info("停止装备OCR功能。") # 日志记录
        
        # 关键修复：在主线程中清理窗口
        self.app.after(0, self.clear_equip_results)
        
        self.equip_last_image_hash = None # 清除哈希，以便下次能立即启动
        if self.app.debug_mode:
            logging.debug("装备OCR已停止，并清理窗口和状态")
            
    def is_running(self):
        """检查装备OCR循环是否正在运行"""
        return self.equip_ocr_running

    def run_equip_ocr_loop(self):
        """
        装备OCR识别的主循环。
        使用动态时序控制器管理运行时间。
        """
        if self.app.debug_mode:
            logging.debug("启动装备OCR...")

        while self.equip_ocr_running:
            try:
                # --- 快速响应停止信号 ---
                if not self.equip_ocr_running:
                    logging.info("[Equip] 检测到停止信号，立即退出循环")
                    break
                
                # --- 动态超时检测 ---
                if not self.ocr_timer.should_continue():
                    logging.info("[Equip] 动态时序控制器指示停止，执行清理")
                    self.stop_equip_ocr()
                    break
                
                loop_start_time = time.time() # 记录循环开始时间
                current_time = time.time()

                game_window_rect = self._get_game_window_rect_cached()
                # [优化] 移除频繁的日志打印，只在缓存更新时才记录
                # logging.info(f"[Equip] 获取游戏窗口坐标: {game_window_rect}")
                # if self.app.debug_mode:
                #     print(f"[EquipManager] 游戏窗口坐标: {game_window_rect}")

                # 步骤一："视觉计数器" - 通过轮廓分析确定装备数量
                contour_start_time = time.time()
                # [修改] 使用新的坐标转换函数
                contour_x1, contour_y1, contour_x2, contour_y2 = utils.convert_relative_to_absolute(
                    config.EQUIP_CONTOUR_REGION_RELATIVE, game_window_rect
                )
                # [优化] 移除频繁的坐标打印，只在调试模式且首次运行时打印
                # if self.app.debug_mode:
                #     print(f"[EquipManager] 轮廓检测区域绝对坐标: ({contour_x1}, {contour_y1}, {contour_x2}, {contour_y2})")
                
                contour_img = ocr_handler.capture_screen((contour_x1, contour_y1, contour_x2, contour_y2))
                
                equip_count = ocr_handler.find_equipment_contours(contour_img)
                contour_duration = time.time() - contour_start_time

                # [新增] 用户提出的关键修正：将6个或更多的错误计数强制修正为5
                if equip_count >= 6:
                    if self.app.debug_mode:
                        logging.debug(f"[EquipManager] 原始计数为 {equip_count}，强制修正为 5 进行尝试。")
                    logging.info(f"[Equip] 轮廓计数过多 (原始: {equip_count})，强制修正为 5 以进行识别。")
                    equip_count = 5
                
                # [新增] 误检测保护：当检测到少量装备且之前有更多装备时，进行额外验证
                if equip_count <= 2 and self.last_stable_equip_count >= 3:
                    # 可能是从装备页面切换到其他页面的误检测
                    logging.info(f"[Equip] 可疑的轮廓变化: {self.last_stable_equip_count} -> {equip_count}，可能是页面切换")
                    
                    # 如果检测到的数量急剧减少，更倾向于认为是误检测
                    if equip_count == 1:
                        logging.info("[Equip] 单个轮廓可能是误检测，直接清理现有窗口")
                        if self.last_stable_equip_count > 0:
                            self._smart_clear_windows()
                        continue

                if self.app.debug_mode:
                    logging.debug(f"[EquipManager] 视觉计数器检测到 {equip_count} 个装备。")
                
                # [优化] 仅在装备数量变化时打印日志
                if equip_count != self.last_logged_equip_count:
                    logging.info(f"[Equip] 视觉计数器检测到 {equip_count} 个装备轮廓。")
                    self.last_logged_equip_count = equip_count

                if equip_count > 0:
                    # [重构] 引入哈希检测，仅在图像变化时才执行OCR
                    current_hash = imagehash.phash(contour_img)
                    if self.equip_last_image_hash is None or utils.hash_distance(current_hash, self.equip_last_image_hash) > self.hash_threshold:
                        self.equip_last_image_hash = current_hash

                        # 步骤二："精准识别器" - 根据数量选择对应的OCR区域
                        ocr_regions = config.EQUIP_OCR_REGIONS_BY_COUNT.get(equip_count)
                        if not ocr_regions:
                            if self.app.debug_mode:
                                logging.warning(f"[EquipManager] 警告: 无法为 {equip_count} 个装备找到预设的OCR区域。")
                            time.sleep(0.2)
                            continue

                        # 并行截图与预处理
                        screenshot_start_time = time.time()
                        images_to_process = []
                        with ThreadPoolExecutor(max_workers=equip_count) as executor:
                            future_to_img = {
                                executor.submit(
                                    ocr_handler.capture_screen,
                                    utils.convert_relative_to_absolute(rel_region, game_window_rect)
                                ): i
                                for i, rel_region in enumerate(ocr_regions)
                            }
                            # 按顺序收集截图结果
                            captured_images = [None] * equip_count
                            for future in as_completed(future_to_img):
                                index = future_to_img[future]
                                try:
                                    captured_images[index] = future.result()
                                except Exception as e:
                                    logging.error(f"截图时出错: {e}")

                        for img in captured_images:
                            if img:
                                images_to_process.append(ocr_handler.equip_preprocess_image(img))
                        screenshot_duration = time.time() - screenshot_start_time

                        # 步骤A: 并行进行精准OCR
                        ocr_start_time = time.time()
                        raw_text_parts, _ = ocr_handler.ocr_recognize(images_to_process, psm=7)
                        ocr_duration = time.time() - ocr_start_time
                        
                        if self.app.debug_mode:
                            logging.debug(f"[EquipManager] 原始OCR识别结果: {raw_text_parts}")

                        # 步骤B: 对OCR结果进行模糊匹配，获取有效装备名称
                        match_start_time = time.time()
                        matched_names = self.equip_query.match_equipment(raw_text_parts, self.equip_whitelist)
                        match_duration = time.time() - match_start_time
                        if self.app.debug_mode:
                            logging.debug(f"[EquipManager] 匹配后的有效装备: {matched_names}")
                            logging.info(f"[Equip] 匹配后的有效装备: {matched_names}")

                        # 步骤C: 验证与渲染
                        valid_matches = [name for name in matched_names if name is not None]
                        
                        if valid_matches:
                            if not self.first_result_logged:
                                total_duration = time.time() - self.equip_start_time
                                logging.info(f"[Equip] 首次识别到结果并显示，总耗时: {total_duration:.2f}s")
                                self.first_result_logged = True

                        # 改进的验证逻辑：对不同装备数量使用不同策略
                        should_display = False
                        
                        if equip_count <= 2:
                            # 1-2个装备：必须全部识别成功，防止误检测
                            should_display = len(valid_matches) == equip_count
                            if not should_display:
                                logging.info(f"[Equip] 少量装备验证失败: {len(valid_matches)}/{equip_count}，可能是误检测")
                        else:
                            # 3+个装备：使用60%阈值
                            should_display = len(valid_matches) >= equip_count * 0.6
                        
                        if should_display:
                            self.equip_no_detect_time = time.time()
                            self.app.after(0, self.update_or_create_equip_windows, matched_names, equip_count)
                            self.last_stable_equip_count = equip_count
                            
                            # 通知动态时序控制器检测到有效结果
                            self.ocr_timer.extend_timer()
                            
                            # 通知时序控制器窗口可见
                            self.ocr_timer.set_windows_visible(True)
                        else:
                            if self.app.debug_mode:
                                logging.debug(f"[EquipManager] 匹配验证失败 ({len(valid_matches)}/{equip_count})，抛弃本次结果。")
                            
                            # 关键修复：强制清理现有窗口，防止残留
                            logging.info(f"[Equip] 当前状态: last_stable_equip_count={self.last_stable_equip_count}, 窗口数量={len(self.equip_overlay_windows)}")
                            if self.last_stable_equip_count > 0:
                                logging.info(f"[Equip] 验证失败，强制清理之前的 {self.last_stable_equip_count} 个窗口")
                                self._smart_clear_windows()
                                self.equip_last_image_hash = None
                                # 通知时序控制器窗口不可见
                                self.ocr_timer.set_windows_visible(False)
                            elif len(self.equip_overlay_windows) > 0:
                                # 即使计数为0，但如果有窗口存在，也要清理
                                logging.info(f"[Equip] 发现 {len(self.equip_overlay_windows)} 个残留窗口，强制清理")
                                self._smart_clear_windows()
                                # 通知时序控制器窗口不可见
                                self.ocr_timer.set_windows_visible(False)
                        
                        # [优化] 仅在有识别任务时才打印详细性能日志
                        loop_duration = time.time() - loop_start_time
                        perf_log = (f"[Equip Perf] Total: {loop_duration:.3f}s | "
                                    f"Contour: {contour_duration:.3f}s | "
                                    f"Screenshot: {screenshot_duration:.3f}s | "
                                    f"OCR: {ocr_duration:.3f}s | "
                                    f"Match: {match_duration:.3f}s")
                        logging.info(perf_log)
                else: # equip_count == 0
                    # [优化] 如果之前有装备，现在视觉计数器返回0，智能清理
                    if self.last_stable_equip_count > 0:
                        logging.info(f"[Equip] 装备数量从 {self.last_stable_equip_count} 变为 0，触发清理")
                        self._smart_clear_windows()
                        # 关键修复：重置哈希，确保下次能正确检测
                        self.equip_last_image_hash = None
                        
                        # 通知时序控制器窗口不可见
                        self.ocr_timer.set_windows_visible(False)
                
                time.sleep(0.1) # 减少sleep时间，提高停止响应速度
                
                # 打印整个循环的耗时
                loop_duration = time.time() - loop_start_time
                

            except Exception as e:
                if self.app.debug_mode:
                    logging.error(f"装备OCR循环出错: {e}", exc_info=True)
                logging.error(f"Equip OCR 主循环异常: {e}", exc_info=True)
                time.sleep(1)
        
        # 循环退出后确保清理资源
        if self.equip_ocr_running:
            logging.info("[Equip] OCR循环退出，执行最终清理")
            self.stop_equip_ocr()

    def update_or_create_equip_windows(self, matched_names, total_count):
        """
        [重构] 更新或创建装备信息窗口。
        - 布局始终由 `total_count` (轮廓检测数量) 决定。
        - `matched_names` 是一个包含None的列表。
        """
        # 使用新的、专门用于评级UI布局的坐标，由轮廓数决定
        regions = config.EQUIP_RATING_REGIONS_BY_COUNT.get(total_count, [])
        
        # [新增] 获取游戏窗口坐标用于位置计算
        game_window_rect = self._get_game_window_rect_cached()
        logging.info(f"[Equip] 更新装备窗口时获取游戏窗口坐标: {game_window_rect}")
        
        # 智能窗口管理：只在必要时重建窗口
        if len(self.equip_overlay_windows) != total_count:
            # 如果窗口数量不匹配，先清理现有窗口
            self._force_clear_windows()
            
            # 创建新窗口
            for _ in range(total_count):
                rating_window = tk.Toplevel(self.app)
                rating_window.overrideredirect(True)
                rating_window.attributes('-alpha', 0.7)
                rating_window.attributes('-topmost', True)
                rating_label = ttk.Label(rating_window, text="", anchor="center")
                rating_label.pack(expand=True, fill=tk.BOTH)
                self.equip_overlay_windows.append(rating_window)

        # 更新窗口内容和位置
        for i, name in enumerate(matched_names):
            if i >= len(regions) or i >= len(self.equip_overlay_windows):
                continue

            # [修改] 使用新的坐标转换函数
            x1, y1, x2, y2 = utils.convert_relative_to_absolute(regions[i], game_window_rect)
            width = x2 - x1
            height = y2 - y1
            # [优化] 移除频繁的坐标打印
            # if self.app.debug_mode:
            #     print(f"[EquipManager] 装备窗口 {i} 绝对坐标: ({x1}, {y1}) 尺寸: {width}x{height}")
            
            rating_window = self.equip_overlay_windows[i]
            rating_label = rating_window.winfo_children()[0]
            
            rating_window.geometry(f"{width}x{height}+{x1}+{y1}")

            # 如果name是None，则显示占位符"?"
            if name is None:
                rating_label.config(text="?", style="EquipRank.TLabel")
            else:
                result = self.equip_query.smart_query(name)
                rating = result.get('rating')
                if rating:
                    rating_label.config(text=rating, style=f"EquipRank{rating}.TLabel")
                else:
                    rating_label.config(text="?", style="EquipRank.TLabel")

            rating_window.deiconify()
            rating_window.lift()

    def clear_equip_results(self):
        """销毁所有装备窗口"""
        logging.info("[Equip] 调用clear_equip_results清理窗口")
        self._force_clear_windows()
        # 通知时序控制器窗口不可见
        self.ocr_timer.set_windows_visible(False)
    
    def _smart_clear_windows(self):
        """智能窗口清理，避免不必要的闪烁"""
        logging.info(f"[Equip] _smart_clear_windows 被调用，当前窗口数量: {len(self.equip_overlay_windows)}")
        
        # 如果没有窗口，直接返回
        if not self.equip_overlay_windows:
            self.last_stable_equip_count = 0
            logging.info("[Equip] 没有窗口需要清理")
            return
            
        # 关键修复：检查窗口是否真的可见，避免重复清理
        visible_windows = []
        all_windows_info = []
        
        for i, window in enumerate(self.equip_overlay_windows):
            try:
                exists = window.winfo_exists()
                mapped = window.winfo_ismapped() if exists else False
                all_windows_info.append(f"窗口{i}: exists={exists}, mapped={mapped}")
                
                if exists and mapped:
                    visible_windows.append(window)
            except tk.TclError as e:
                all_windows_info.append(f"窗口{i}: TclError={e}")
                
        logging.info(f"[Equip] 窗口状态检查: {'; '.join(all_windows_info)}")
        logging.info(f"[Equip] 发现 {len(visible_windows)} 个可见窗口")
                
        if not visible_windows:
            # 如果没有可见窗口，只重置状态
            self.last_stable_equip_count = 0
            logging.info("[Equip] 没有可见窗口，只重置状态")
            return
            
        logging.info(f"[Equip] 开始智能清理 {len(visible_windows)} 个可见装备窗口")
        
        # 关键修复：先隐藏再销毁，借鉴海克斯的成功经验
        self.app.after(0, self._hide_then_destroy_windows)
    
    def _hide_then_destroy_windows(self):
        """先隐藏再销毁窗口，借鉴海克斯的成功经验"""
        window_count = len(self.equip_overlay_windows)
        
        # 第一步：立即隐藏所有窗口
        for window in self.equip_overlay_windows:
            try:
                if window.winfo_exists():
                    window.withdraw()
            except tk.TclError:
                pass
        
        # 第二步：延迟销毁窗口，给UI时间响应
        def _delayed_destroy():
            for window in self.equip_overlay_windows:
                try:
                    window.destroy()
                except tk.TclError:
                    pass
            self.equip_overlay_windows = []
            self.last_stable_equip_count = 0
            
            if window_count > 0:
                logging.info(f"[Equip] 已清理 {window_count} 个装备窗口")
        
        # 50ms延迟销毁，确保隐藏操作先完成
        self.app.after(50, _delayed_destroy)
    
    def _force_clear_windows(self):
        """强制清理所有窗口（立即销毁）"""
        window_count = len(self.equip_overlay_windows)
        for window in self.equip_overlay_windows:
            try:
                window.destroy()
            except tk.TclError:
                pass
        self.equip_overlay_windows = []
        self.last_stable_equip_count = 0
        
        if window_count > 0:
            logging.info(f"[Equip] 已强制清理 {window_count} 个装备窗口")

    def destroy(self):
        """
        快速销毁EquipManager资源（已由主程序处理，此方法保留兼容性）
        """
        logging.info("[Equip] 装备管理器资源清理完成")
        self.equip_ocr_running = False
        # 主程序已经处理了窗口销毁，这里只需要停止循环

    def _get_game_window_rect_cached(self, force_update=False):
        """
        [新增] 获取缓存的游戏窗口坐标，减少频繁的API调用。
        
        Args:
            force_update (bool): 是否强制更新缓存
            
        Returns:
            tuple: (x, y, width, height) 游戏窗口坐标
        """
        current_time = time.time()
        
        # 判断是否需要更新缓存
        if (force_update or 
            self.cached_game_window_rect is None or 
            current_time - self.last_window_check_time > self.window_check_interval):
            
            # 获取新的窗口坐标
            self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
            self.last_window_check_time = current_time
            
            if self.app.debug_mode:
                logging.debug(f"[EquipManager] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
            logging.info(f"[Equip] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
        
        return self.cached_game_window_rect

    def _start_periodic_window_check(self):
        """启动定期窗口检查机制（保险措施）。"""
        if self.equip_ocr_running:
            self._periodic_window_check()
            # 每5秒检查一次
            self.app.after(5000, self._start_periodic_window_check)

    def _periodic_window_check(self):
        """定期检查窗口状态，确保没有评分时窗口正确关闭。"""
        try:
            if not self.equip_ocr_running:
                return

            # 检查是否有有效的评分结果
            has_valid_results = False
            if hasattr(self, 'last_stable_equip_count') and self.last_stable_equip_count > 0:
                has_valid_results = True

            # 检查窗口是否可见
            windows_visible = self._check_windows_visibility()

            # 如果窗口可见但没有有效评分，强制隐藏窗口
            if windows_visible and not has_valid_results:
                logging.warning("[Equip] 定期检查发现窗口可见但无有效评分，强制隐藏窗口")
                self._smart_clear_windows()

        except Exception as e:
            logging.error(f"[Equip] 定期窗口检查出错: {e}", exc_info=True)

    def _check_windows_visibility(self):
        """
        检查是否有装备窗口当前可见

        Returns:
            bool: True表示有窗口可见，False表示所有窗口都不可见
        """
        try:
            # 检查装备评级窗口
            for window in self.equip_overlay_windows:
                if window and window.winfo_exists() and window.winfo_ismapped():
                    return True

            return False
        except Exception as e:
            logging.error(f"[Equip] 检查窗口可见性时出错: {e}")
            return False
