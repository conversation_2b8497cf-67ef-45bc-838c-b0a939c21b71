<template>
  <!-- 果实页面内容 -->
  <div class="powerup-page">

    <!-- 搜索区域 -->
    <div class="search-section">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索果实名称..."
        class="search-input"
      />
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <!-- 评级筛选 -->
      <div class="filter-area">
        <button
          v-for="tier in tierFilters"
          :key="tier.value"
          class="category-filter-button"
          :class="[
            { 'active': selectedTiers.includes(tier.value) },
            getTierFilterClass(tier.value)
          ]"
          @click="toggleTierFilter(tier.value)"
        >
          {{ tier.label }}
        </button>
      </div>
    </div>

    <!-- 果实列表区域 -->
    <div class="powerup-list-area" @scroll="handleScroll" ref="scrollContainer">



      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载果实数据...</p>
      </div>

      <!-- 暂无数据提示 -->
      <div v-else-if="allPowerups.length === 0" class="no-data-state">
        <div class="no-data-icon">🍎</div>
        <h3>暂无果实数据</h3>
        <p>数据库连接正常，但暂未加载到果实数据</p>
        <button @click="loadPowerups" class="retry-button">重新加载</button>
      </div>

      <!-- 果实分组显示 -->
      <div v-else class="powerup-groups" ref="powerupGroupsRef">

        <!-- 浮动评级指示器 -->
        <div v-if="currentVisibleTier" class="floating-tier-indicator">
          <div
            class="tier-indicator-badge"
            :class="getTierIndicatorClass(currentVisibleTier)"
          >
            {{ currentVisibleTier }}级
          </div>
        </div>

        <!-- 果实分组内容 -->
        <div
          v-for="tier in visibleTiers"
          :key="tier"
          class="tier-group"
          :data-tier="tier"
        >
          <!-- 果实网格 -->
          <div class="powerup-grid">
            <div
              v-for="powerup in getPowerupsByTier(tier)"
              :key="powerup.powerup_name"
              class="powerup-card"
              :class="getTierClass(powerup.tier_rank)"
              @click="handlePowerupClick(powerup)"
              @mouseenter="handlePowerupMouseEnter(powerup, $event)"
              @mouseleave="handlePowerupMouseLeave"
              @mousemove="handlePowerupMouseMove"
            >
              <!-- 果实名称 -->
              <div class="powerup-name">
                {{ powerup.powerup_name || '未知果实' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 底部空白区域，确保滚动到底部时有足够空间 -->
        <div class="bottom-spacer"></div>
      </div>
    </div>

    <!-- 悬停提示框 -->
    <Teleport to="body">
      <div
        v-if="showTooltip && hoveredPowerup"
        class="powerup-tooltip"
        :style="{
          left: tooltipPosition.x + 'px',
          top: tooltipPosition.y + 'px'
        }"
      >
        <div class="tooltip-header">
          <h4 class="tooltip-title">{{ hoveredPowerup.powerup_name }}</h4>
          <span class="tooltip-tier" :class="getTierClass(hoveredPowerup.tier_rank)">
            {{ hoveredPowerup.tier_rank }}
          </span>
        </div>
        <div
          v-if="hoveredPowerup.description"
          class="tooltip-description"
        >
          {{ hoveredPowerup.description }}
        </div>
        <div
          v-else
          class="tooltip-description no-description"
        >
          暂无详细说明
        </div>
        <div class="tooltip-stats" v-if="hoveredPowerup.hero_count">
          <span class="stat-text">适用英雄: {{ hoveredPowerup.hero_count }}个</span>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import ColorLegend from '@/components/common/ColorLegend.vue'
import { useData } from '@/composables/useData'
import { useDataStore } from '@/stores/data'

// 定义果实数据类型
interface PowerupData {
  powerup_name: string
  tier_rank: string
  description?: string
  min_stage?: number
  max_stage?: number
  weight?: number
  hero_count?: number
}

// Emits
const emit = defineEmits<{
  powerupSelected: [powerupName: string]
}>()

// === 数据组合式函数 ===
const { getPowerupList } = useData()

// === 果实数据状态 ===
const allPowerups = ref<PowerupData[]>([])
const isLoading = ref(true)
const searchQuery = ref('')
const selectedTiers = ref<string[]>(['all'])

// === 悬停提示状态 ===
const hoveredPowerup = ref<PowerupData | null>(null)
const tooltipPosition = ref({ x: 0, y: 0 })
const showTooltip = ref(false)

// === 滚动和浮动指示器状态 ===
const powerupGroupsRef = ref<HTMLElement | null>(null)
const scrollContainer = ref<HTMLElement | null>(null)
const currentVisibleTier = ref<string | null>(null)

// === 评级筛选配置 ===
const tierFilters = [
  { label: '全部', value: 'all' },
  { label: 'S级', value: 'S' },
  { label: 'A级', value: 'A' },
  { label: 'B级', value: 'B' },
  { label: 'C级', value: 'C' },
  { label: 'D级', value: 'D' }
]

// === 计算属性 ===
const filteredPowerups = computed(() => {
  let powerups = allPowerups.value

  // 首先排除Unknown等级的果实
  powerups = powerups.filter(powerup =>
    powerup.tier_rank && powerup.tier_rank !== 'Unknown'
  )

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    powerups = powerups.filter(powerup =>
      powerup.powerup_name?.toLowerCase().includes(query)
    )
  }

  if (!selectedTiers.value.includes('all') && selectedTiers.value.length > 0) {
    powerups = powerups.filter(powerup =>
      selectedTiers.value.includes(powerup.tier_rank || '')
    )
  }

  return powerups
})

const getPowerupsByTier = (tier: string) => {
  return filteredPowerups.value
    .filter(powerup => powerup.tier_rank === tier)
    .sort((a, b) => (a.powerup_name || '').localeCompare(b.powerup_name || ''))
}

// 计算应该显示的评级组
const visibleTiers = computed(() => {
  const allTiers = ['S', 'A', 'B', 'C', 'D'] // 排除 Unknown

  return allTiers.filter(tier => {
    const powerupsInTier = getPowerupsByTier(tier)

    // 如果该评级组没有果实，不显示
    if (powerupsInTier.length === 0) {
      return false
    }

    // 如果选择了"全部"，显示所有有果实的评级组
    if (selectedTiers.value.includes('all')) {
      return true
    }

    // 如果没有选择任何评级，显示所有有果实的评级组
    if (selectedTiers.value.length === 0) {
      return true
    }

    // 如果选择了特定评级，只显示选中的评级组
    return selectedTiers.value.includes(tier)
  })
})

// === 评级筛选功能 ===
const toggleTierFilter = (tier: string) => {
  if (tier === 'all') {
    selectedTiers.value = ['all']
  } else {
    const index = selectedTiers.value.indexOf(tier)
    if (index > -1) {
      selectedTiers.value.splice(index, 1)
      // 如果没有选择任何评级，回到"全部"
      if (selectedTiers.value.length === 0) {
        selectedTiers.value = ['all']
      }
    } else {
      // 移除"全部"选项
      const allIndex = selectedTiers.value.indexOf('all')
      if (allIndex > -1) {
        selectedTiers.value.splice(allIndex, 1)
      }
      selectedTiers.value.push(tier)
    }
  }

  console.log('当前选择的评级:', selectedTiers.value)


}

// === 样式辅助函数 ===
const getTierLabelClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s-label',
    'A': 'tier-a-label',
    'B': 'tier-b-label',
    'C': 'tier-c-label',
    'D': 'tier-d-label'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-d-label'
}

const getTierIndicatorClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-indicator-s',
    'A': 'tier-indicator-a',
    'B': 'tier-indicator-b',
    'C': 'tier-indicator-c',
    'D': 'tier-indicator-d'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-indicator-d'
}

const getTierFilterClass = (tier: string) => {
  if (tier === 'all') return 'filter-all'
  const tierClasses = {
    'S': 'filter-tier-s',
    'A': 'filter-tier-a',
    'B': 'filter-tier-b',
    'C': 'filter-tier-c',
    'D': 'filter-tier-d'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'filter-tier-d'
}

const getTierClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s',
    'A': 'tier-a',
    'B': 'tier-b',
    'C': 'tier-c',
    'D': 'tier-d'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-d'
}

// === 悬停处理 ===
const handlePowerupMouseEnter = (powerup: PowerupData, event: MouseEvent) => {
  hoveredPowerup.value = powerup
  updateTooltipPosition(event)
  showTooltip.value = true
}

const handlePowerupMouseLeave = () => {
  showTooltip.value = false
  hoveredPowerup.value = null
}

const handlePowerupMouseMove = (event: MouseEvent) => {
  if (showTooltip.value) {
    updateTooltipPosition(event)
  }
}

const updateTooltipPosition = (event: MouseEvent) => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const tooltipWidth = 350 // 提示框的大概宽度
  const tooltipHeight = 200 // 提示框的大概高度

  // 使用2/3分割点决定提示框显示位置
  const showOnLeft = event.clientX > windowWidth * 2 / 3

  // 计算水平位置，让提示框更靠近鼠标
  let x = showOnLeft ? event.clientX - tooltipWidth - 5 : event.clientX + 5

  // 确保提示框不会超出屏幕边界
  if (x < 10) x = 10
  if (x + tooltipWidth > windowWidth - 10) x = windowWidth - tooltipWidth - 10

  // 计算垂直位置，让提示框更靠近鼠标
  let y = event.clientY - 5

  // 确保提示框不会超出屏幕底部
  if (y + tooltipHeight > windowHeight - 10) {
    y = event.clientY - tooltipHeight + 5
  }

  // 确保提示框不会超出屏幕顶部
  if (y < 10) y = 10

  tooltipPosition.value = { x, y }
}

// === 滚动处理 ===
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement



  // 查找当前可见的评级组 - 以顶部位置为准
  const tierGroups = target.querySelectorAll('.tier-group')
  let visibleTier = ''

  // 获取容器的顶部位置
  const containerRect = target.getBoundingClientRect()
  const containerTop = containerRect.top

  // 定义检测点：容器顶部向下一点点的位置
  const detectionPoint = containerTop + 20 // 20px的偏移量，避免边界问题

  // 找到在检测点位置的评级组
  for (const group of tierGroups) {
    const rect = group.getBoundingClientRect()

    // 如果检测点在这个评级组的范围内
    if (rect.top <= detectionPoint && rect.bottom > detectionPoint) {
      visibleTier = (group as HTMLElement).dataset.tier || ''
      break
    }
  }

  // 如果没有找到，则找最接近顶部的评级组
  if (!visibleTier) {
    let closestGroup = null
    let minDistance = Infinity

    for (const group of tierGroups) {
      const rect = group.getBoundingClientRect()
      const distance = Math.abs(rect.top - containerTop)

      if (distance < minDistance && rect.bottom > containerTop) {
        minDistance = distance
        closestGroup = group
      }
    }

    if (closestGroup) {
      visibleTier = (closestGroup as HTMLElement).dataset.tier || ''
    }
  }

  currentVisibleTier.value = visibleTier
  console.log('当前可见评级:', visibleTier, '容器顶部:', containerTop)
}



// === 果实点击处理 ===
const handlePowerupClick = (powerup: PowerupData) => {
  console.log('点击果实:', powerup.powerup_name)
  if (powerup.powerup_name) {
    emit('powerupSelected', powerup.powerup_name)
  }
}

// === 数据加载 ===
const loadPowerups = async () => {
  console.log('🔍 开始加载果实数据...')

  try {
    // 先尝试同步获取缓存数据，避免设置loading状态
    const dataStore = useDataStore()
    const cachedData = dataStore.getCachedQuery('powerup_list')

    if (cachedData && cachedData.length > 0) {
      console.log('✅ 从缓存即时加载果实数据，零延迟')
      allPowerups.value = cachedData
      return
    }

    // 缓存未命中时才显示loading
    console.log('缓存未命中，显示loading并从数据库加载...')
    isLoading.value = true

    const results = await invoke('get_powerup_list')
    console.log('📋 果实数据加载结果:', results)
    
    // 检查是否是QueryResult格式
    if (results && typeof results === 'object' && 'data' in results) {
      console.log('📋 检测到QueryResult格式')
      
      if ('error' in results && results.error) {
        console.error('❌ 数据库查询错误:', results.error)
        allPowerups.value = []
        return
      }
      
      if (Array.isArray(results.data)) {
        allPowerups.value = results.data
        console.log(`✅ 成功加载 ${allPowerups.value.length} 个果实`)
        if (results.data.length > 0) {
          console.log('🍎 第一个果实示例:', results.data[0])
        }
      } else {
        console.warn('⚠️ QueryResult.data 不是数组格式:', results.data)
        allPowerups.value = []
      }
    } else if (Array.isArray(results)) {
      allPowerups.value = results
      console.log(`✅ 成功加载 ${allPowerups.value.length} 个果实`)
      if (results.length > 0) {
        console.log('🍎 第一个果实示例:', results[0])
      }
    } else {
      console.warn('⚠️ 返回的数据格式不正确:', results)
      allPowerups.value = []
    }
  } catch (error) {
    console.error('❌ 加载果实数据失败:', error)
    allPowerups.value = []
  } finally {
    isLoading.value = false


  }
}

// === 监听搜索查询变化 ===
watch(searchQuery, () => {
  // 搜索条件改变时的处理
})

// === 组件挂载时加载数据 ===
onMounted(() => {
  loadPowerups()
})
</script>

<style scoped>
/* === 果实页面 === */
.powerup-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
  height: 100%; /* 确保页面占满可用高度 */
  max-height: 100vh; /* 防止超出视口 */
}

/* === 搜索区域 === */
.search-section {
  flex-shrink: 0;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* === 筛选区域 === */
.filter-section {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-area {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  padding: 0.5rem;
}

.category-filter-button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 60px;
  text-align: center;
}

.category-filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-filter-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-filter-button:hover::before {
  left: 100%;
}

.category-filter-button.active {
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

/* 全部按钮激活状态 */
.filter-all.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3));
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* S级按钮激活状态 */
.filter-tier-s.active {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.8), rgba(255, 71, 87, 0.8));
  border-color: rgba(255, 107, 157, 0.8);
  box-shadow: 0 0 20px rgba(255, 107, 157, 0.4);
}

/* A级按钮激活状态 */
.filter-tier-a.active {
  background: linear-gradient(135deg, rgba(255, 167, 38, 0.8), rgba(255, 143, 0, 0.8));
  border-color: rgba(255, 167, 38, 0.8);
  box-shadow: 0 0 20px rgba(255, 167, 38, 0.4);
}

/* B级按钮激活状态 */
.filter-tier-b.active {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.8), rgba(255, 193, 7, 0.8));
  border-color: rgba(255, 235, 59, 0.8);
  box-shadow: 0 0 20px rgba(255, 235, 59, 0.4);
}

/* C级按钮激活状态 */
.filter-tier-c.active {
  background: linear-gradient(135deg, rgba(205, 220, 57, 0.8), rgba(139, 195, 74, 0.8));
  border-color: rgba(205, 220, 57, 0.8);
  box-shadow: 0 0 20px rgba(205, 220, 57, 0.4);
}

/* D级按钮激活状态 */
.filter-tier-d.active {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.8), rgba(46, 125, 50, 0.8));
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.4);
}



/* === 果实列表区域 === */
.powerup-list-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0.5rem;
  position: relative;
  height: 0; /* 强制flex子元素计算高度 */
  min-height: 200px; /* 最小高度保证可见性 */
}

/* 确保滚动条始终可见且样式明显 */
.powerup-list-area::-webkit-scrollbar {
  width: 6px;
  background: rgba(255, 255, 255, 0.05);
}

.powerup-list-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin: 2px;
}

.powerup-list-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 20px;
}

.powerup-list-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}

.powerup-list-area::-webkit-scrollbar-thumb:active {
  background: rgba(255, 255, 255, 0.8);
}

/* === 滚动指示器 === */
.scroll-indicator {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 50;
  pointer-events: none;
  animation: scrollIndicatorFade 0.5s ease-out;
}

@keyframes scrollIndicatorFade {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.scroll-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 12px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  animation: scrollHintPulse 2s ease-in-out infinite;
}

@keyframes scrollHintPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}



/* === 浮动评级指示器 === */
.floating-tier-indicator {
  position: fixed;
  left: 50%;
  top: 14rem; /* 向下移动2rem */
  transform: translateX(-50%);
  z-index: 100;
  pointer-events: none;
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.tier-indicator-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tier-indicator-s {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 71, 87, 0.9));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.tier-indicator-a {
  background: linear-gradient(135deg, rgba(255, 167, 38, 0.9), rgba(255, 143, 0, 0.9));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.tier-indicator-b {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 193, 7, 0.9));
  color: #000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tier-indicator-c {
  background: linear-gradient(135deg, rgba(205, 220, 57, 0.9), rgba(139, 195, 74, 0.9));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.tier-indicator-d {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}



/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 暂无数据状态 === */
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-data-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.no-data-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 果实分组 === */
.powerup-groups {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  /* 移除 min-height: 100vh，让内容自然撑开 */
}

/* === 底部空白区域 === */
.bottom-spacer {
  height: 6rem; /* 提供底部滚动空间 */
  flex-shrink: 0;
}

.tier-group {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  min-height: 100px;
  position: relative;
}

.tier-group::before {
  content: attr(data-tier) '级';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rem;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
  font-size: 14px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 5;
  border-radius: 12px 12px 0 0;
}

.tier-group[data-tier="S"]::before {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 71, 87, 0.9));
  border-bottom: 2px solid rgba(255, 107, 157, 0.8);
}

.tier-group[data-tier="A"]::before {
  background: linear-gradient(135deg, rgba(255, 167, 38, 0.9), rgba(255, 143, 0, 0.9));
  border-bottom: 2px solid rgba(255, 167, 38, 0.8);
}

.tier-group[data-tier="B"]::before {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 193, 7, 0.9));
  border-bottom: 2px solid rgba(255, 235, 59, 0.8);
}

.tier-group[data-tier="C"]::before {
  background: linear-gradient(135deg, rgba(205, 220, 57, 0.9), rgba(139, 195, 74, 0.9));
  border-bottom: 2px solid rgba(205, 220, 57, 0.8);
}

.tier-group[data-tier="D"]::before {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9));
  border-bottom: 2px solid rgba(76, 175, 80, 0.8);
}



/* === 果实网格 === */
.powerup-grid {
  padding: 2.75rem 0.5rem 0.5rem 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  gap: 0.375rem;
  align-content: start;
}

/* === 果实卡片 === */
.powerup-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  z-index: 1;
  min-height: 65px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.4rem;
}

.powerup-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

/* 评级特定的悬停效果 */
.powerup-card.tier-s:hover {
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

.powerup-card.tier-a:hover {
  border-color: rgba(255, 69, 0, 0.6);
  box-shadow: 0 8px 25px rgba(255, 69, 0, 0.2);
}

.powerup-card.tier-b:hover {
  border-color: rgba(0, 191, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 191, 255, 0.2);
}

.powerup-card.tier-c:hover {
  border-color: rgba(50, 205, 50, 0.6);
  box-shadow: 0 8px 25px rgba(50, 205, 50, 0.2);
}

.powerup-card.tier-d:hover {
  border-color: rgba(211, 211, 211, 0.6);
  box-shadow: 0 8px 25px rgba(211, 211, 211, 0.2);
}



/* === 果实名称 === */
.powerup-name {
  color: white;
  font-size: 11px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
  word-break: break-word;
  text-align: center;
  margin-top: 0.25rem;
}

/* === 悬停提示框 === */
.powerup-tooltip {
  position: fixed;
  z-index: 9999;
  max-width: 350px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  gap: 0.75rem;
}

.tooltip-title {
  font-size: 16px;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  flex: 1;
}

.tooltip-tier {
  font-size: 12px;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  flex-shrink: 0;
}

.tooltip-tier.tier-s { background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 71, 87, 0.9)); color: white; }
.tooltip-tier.tier-a { background: linear-gradient(135deg, rgba(255, 167, 38, 0.9), rgba(255, 143, 0, 0.9)); color: white; }
.tooltip-tier.tier-b { background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 193, 7, 0.9)); color: #000; }
.tooltip-tier.tier-c { background: linear-gradient(135deg, rgba(205, 220, 57, 0.9), rgba(139, 195, 74, 0.9)); color: white; }
.tooltip-tier.tier-d { background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9)); color: white; }


.tooltip-description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 0.5rem;
  white-space: pre-line;
}

.tooltip-description.no-description {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.tooltip-stats {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.stat-text {
  display: block;
}
</style>
