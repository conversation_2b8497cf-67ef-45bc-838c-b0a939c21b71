#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import asyncio
import traceback
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from pathlib import Path
import hashlib
import requests
from datetime import datetime
import locale # 修复编码问题所需

# 导入数据库建立功能
try:
    from 数据库建立 import build_database
    has_database_builder = True
except ImportError:
    has_database_builder = False
    print("警告: 无法导入数据库建立模块")

# ========== 制品库配置 ==========
REPO_CONFIG = {
    "package": "shujugengxin/yu",
    "repo": "g-iuob0664-generic",
    "version_url": "https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/version.json"
}

# 数据库配置
DATABASE_CONFIG = {
    'complete_database': {
        'name': '完整数据库',
        'db_file': 'tft_data.db'
    }
}

# ========== 制品库相关函数 ==========
def get_md5(file_path):
    """计算文件MD5值（增加重试机制）"""
    for retry in range(3):
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            if retry == 2:
                print(f"[错误] 计算MD5失败: {e}")
                return None
            time.sleep(1)

def upload_file(file_path, module_name="database"):
    """上传文件到制品库"""
    try:
        # 检查环境变量
        coding_token = os.getenv('CODING_TOKEN')
        if not coding_token:
            print("[错误] 未设置 CODING_TOKEN 环境变量")
            return False
            
        # 构建上传URL
        file_name = os.path.basename(file_path)
        md5 = get_md5(file_path)
        if not md5:
            return False
            
        # 构建上传URL
        upload_url = f"https://{REPO_CONFIG['repo']}.pkg.coding.net/{REPO_CONFIG['package']}/{file_name}"
        
        print(f"[上传] 正在上传 {file_name} 到制品库...")
        
        headers = {
            "Authorization": f"token {coding_token}",
            "Content-Type": "application/octet-stream"
        }
        
        with open(file_path, 'rb') as f:
            response = requests.put(upload_url, headers=headers, data=f)
            
        if response.status_code in [200, 201]:
            print(f"[成功] {file_name} 上传成功")
            return True
        else:
            print(f"[错误] {file_name} 上传失败: HTTP {response.status_code}, {response.text}")
            return False
    except Exception as e:
        print(f"[错误] 上传文件异常: {e}")
        return False

def show_upload_message(title, message, message_type=messagebox.INFO):
    """显示上传消息框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 设置窗口置顶
    
    if message_type == messagebox.INFO:
        messagebox.showinfo(title=title, message=message, master=root)
    elif message_type == messagebox.WARNING:
        messagebox.showwarning(title=title, message=message, master=root)
    elif message_type == messagebox.ERROR:
        messagebox.showerror(title=title, message=message, master=root)
    else:
        messagebox.showinfo(title=title, message=message, master=root)
    
    root.destroy()

# ========== 版本控制类 ==========
class VersionController:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        modules_dir = self.base_dir / "modules"
        modules_dir.mkdir(exist_ok=True)
        self.version_file = modules_dir / "version.json"
        self.versions = self.initialize_version_file()

    def initialize_version_file(self):
        if not self.version_file.exists():
            base_structure = {
                'complete_database': {
                    "version": "00000000.0",
                    "checksum": "",
                    "url": ""
                }
            }
            self.save_versions(base_structure)
            return base_structure
        else:
            return self.load_versions()

    def load_versions(self):
        try:
            with open(self.version_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取版本文件失败: {e}")
            return {}

    def save_versions(self, data):
        try:
            with open(self.version_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存版本文件失败: {e}")

    def get_current_version(self, db_type='complete_database'):
        return self.versions.get(db_type, {}).get("version", "00000000.0")

    def update_version(self, db_type, checksum, url):
        current_ver = self.get_current_version(db_type)
        new_ver = self.generate_version(current_ver)

        if db_type not in self.versions:
            self.versions[db_type] = {}
            
        self.versions[db_type] = {
            "version": new_ver,
            "checksum": checksum,
            "url": url
        }
        self.save_versions(self.versions)
        return new_ver

    def generate_version(self, current_ver):
        today = datetime.now().strftime("%Y%m%d")
        if current_ver.startswith("00000000"):
            return f"{today}.1"
        if current_ver.startswith(today):
            seq = int(current_ver.split('.')[1]) + 1
            return f"{today}.{seq}"
        return f"{today}.1"

# 导入每个模块的获取函数
try:
    from 海克斯 import get_hextech_data
    has_hextech = True
except ImportError:
    has_hextech = False
    print("警告: 无法导入海克斯模块")

try:
    from 羁绊 import get_trait_data
    has_trait = True
except ImportError:
    has_trait = False
    print("警告: 无法导入羁绊模块")

# --- 装备模块重构 ---
# 不再直接导入 get_equipment_data 函数
# 改为导入新的高清图标下载函数，并准备通过子进程调用装备分类脚本
try:
    from 高清图标下载 import download_item_icons, set_logger as set_downloader_logger
    has_item_downloader = True
    print("成功导入新版装备图标下载模块")
except ImportError:
    has_item_downloader = False
    print("警告: 无法导入高清图标下载模块")

# 用于执行装备分类脚本的子进程模块
import subprocess

try:
    from 英雄 import get_hero_data, run_hero_equipment_scraper
    has_hero = True
    has_hero_equips = True
except ImportError:
    has_hero = False
    has_hero_equips = False
    print("警告: 无法导入英雄模块")

try:
    from 阵容 import get_comp_data
    has_comp = True
except ImportError:
    has_comp = False
    print("警告: 无法导入阵容模块")

try:
    import asyncio
    import sys
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from 果实评级_新版 import main as powerup_main
    has_powerup = True
except ImportError:
    has_powerup = False
    print("警告: 无法导入果实评级模块")

class DataCollector:
    """云顶之奕数据收集器，整合各个模块"""
    
    def __init__(self, base_dir=None):
        # 设置基础目录(默认为脚本所在目录)
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        
        # 存储各模块获取的数据
        self.data = {
            "海克斯": {},
            "羁绊": {},
            "装备": {},
            "英雄": {},
            "英雄装备": {},
            "阵容": {},
            "果实": {}
        }
        
        # 模块可用性
        self.modules_available = {
            "海克斯": has_hextech,
            "羁绊": has_trait,
            "装备": True, # 装备模块现在由新的工作流控制，始终视为可用
            "英雄": has_hero,
            "英雄装备": has_hero_equips,
            "阵容": has_comp,
            "果实": has_powerup
        }
        
        # 进度信息
        self.progress_info = {
            "current": 0,
            "total": 0,
            "message": "",
            "status": "空闲"
        }
        
        # 初始化版本控制器
        self.version_controller = VersionController(self.base_dir)
        
        # 创建必要的目录结构
        os.makedirs(os.path.join(self.base_dir, "映射文件"), exist_ok=True)
    
    def rebuild_database(self):
        """重建数据库"""
        if not has_database_builder:
            print("[错误] 数据库建立模块不可用，无法重建数据库")
            return False
            
        print("开始重建数据库...")
        self.progress_info["status"] = "正在重建数据库"
        self.progress_info["message"] = "重建数据库中，请稍候..."
        
        try:
            # 切换到正确的工作目录
            original_cwd = os.getcwd()
            os.chdir(self.base_dir)
            
            # 执行数据库重建
            build_database()
            
            # 恢复工作目录
            os.chdir(original_cwd)
            
            print("数据库重建完成")
            return True
        except Exception as e:
            print(f"数据库重建失败: {e}")
            traceback.print_exc()
            # 确保恢复工作目录
            try:
                os.chdir(original_cwd)
            except:
                pass
            return False
    
    def upload_database(self):
        """上传数据库到制品库"""
        print("开始上传数据库到制品库...")
        self.progress_info["status"] = "正在上传数据库"
        
        # 检查数据库文件是否存在
        db_file_path = os.path.join(self.base_dir, DATABASE_CONFIG['complete_database']['db_file'])
        if not os.path.exists(db_file_path):
            print(f"[错误] 数据库文件不存在: {db_file_path}")
            return False
        
        try:
            # 上传数据库文件
            success = upload_file(db_file_path, "complete_database")
            
            if success:
                # 计算校验和并更新版本
                checksum = get_md5(db_file_path)
                if checksum:
                    download_url = f"https://{REPO_CONFIG['repo']}.pkg.coding.net/{REPO_CONFIG['package']}/{DATABASE_CONFIG['complete_database']['db_file']}?version=latest"
                    new_version = self.version_controller.update_version('complete_database', checksum, download_url)
                    print(f"版本已更新为: {new_version}")
                    
                    # 上传版本文件
                    version_upload_success = upload_file(str(self.version_controller.version_file), "version")
                    if version_upload_success:
                        print("版本文件上传成功")
                    else:
                        print("版本文件上传失败")
                
                print("数据库上传完成")
                return True
            else:
                print("数据库上传失败")
                return False
                
        except Exception as e:
            print(f"上传数据库时出错: {e}")
            traceback.print_exc()
            return False
    
    def get_hextech_data(self, force_update=False, download_icons=False):
        """获取海克斯科技数据"""
        if not has_hextech:
            print("错误: 海克斯模块不可用")
            return False
        
        print("开始获取海克斯科技数据...")
        self.progress_info["status"] = "正在获取海克斯科技数据"
        
        # 修改调用方式，确保传递force_update和download_icons参数
        success, data, mapping = get_hextech_data(
            base_dir=self.base_dir,
            debug_mode=False,
            max_retries=3,
            timeout=30,
            download_icons=download_icons,  # 传递下载图标参数
            force_update=force_update  # 传递强制更新参数
        )
        
        if success:
            self.data["海克斯"]["数据"] = data
            self.data["海克斯"]["映射"] = mapping
            print(f"成功获取 {len(data)} 条海克斯科技数据")
        else:
            print("获取海克斯科技数据失败")
        
        return success
    
    def get_trait_data(self, force_update=False, download_icons=False):
        """获取羁绊数据

        注意：羁绊模块暂不支持图标下载功能，download_icons参数被忽略
        """
        if not has_trait:
            print("错误: 羁绊模块不可用")
            return False

        print("开始获取羁绊数据...")
        self.progress_info["status"] = "正在获取羁绊数据"
        
        # 如果是强制更新模式，则先删除映射文件
        if force_update:
            mapping_file = os.path.join(self.base_dir, "映射文件", "羁绊映射.json")
            if os.path.exists(mapping_file):
                os.remove(mapping_file)
                print(f"强制更新模式：已删除旧的羁绊映射文件 {mapping_file}")
        
        # 调用羁绊模块获取数据
        success, data = get_trait_data(self.base_dir)
        
        if success:
            self.data["羁绊"] = data
            print(f"成功获取 {len(data)} 条羁绊数据")
        else:
            print("获取羁绊数据失败")
        
        return success
    
    def run_equipment_classifier(self, mode='interactive', base_dir=None):
        """
        通过子进程运行装备分类脚本。
        
        Args:
            mode (str): 'interactive' 或 'full'
            base_dir (str): 脚本运行的基础目录
        
        Returns:
            bool: 子进程是否成功执行 (返回码为0)
        """
        base_dir = base_dir or self.base_dir
        script_path = os.path.join(os.path.dirname(__file__), "装备分类.py")
        
        if not os.path.exists(script_path):
            print(f"错误: 装备分类脚本未找到: {script_path}")
            return False
            
        try:
            command = [
                sys.executable,  # 使用当前Python解释器
                script_path,
                f"--mode={mode}",
                f"--dir={base_dir}"
            ]
            print(f"即将执行命令: {' '.join(command)}")
            
            # 对于交互模式，我们需要一个新的控制台窗口
            # 对于全量模式，我们在后台运行并捕获输出
            if mode == 'interactive':
                # CREATE_NEW_CONSOLE使得脚本在独立窗口运行，不阻塞GUI
                process = subprocess.Popen(command, creationflags=subprocess.CREATE_NEW_CONSOLE)
                # 对于交互模式，我们不等待它完成，直接返回成功让用户操作
                print("交互式分类工具已在新的控制台窗口中启动。")
                return True
            else: # full mode
                # 修复：在Windows上，子进程的控制台输出默认使用系统本地编码（如GBK），而不是UTF-8。
                # 我们需要使用 locale.getpreferredencoding() 来获取正确的编码进行解码。
                preferred_encoding = locale.getpreferredencoding(False)
                print(f"系统首选编码: {preferred_encoding}。将使用此编码读取子进程日志。")

                # 使用 Popen 运行并等待其完成，以便捕获日志
                process = subprocess.Popen(
                    command, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.STDOUT, 
                    text=True, 
                    encoding=preferred_encoding, # 使用检测到的正确编码
                    errors='ignore', # 增加容错，忽略无法解码的罕见字符
                    bufsize=1
                )
                
                # 实时读取子进程的输出并打印到主GUI的日志
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        print(output.strip())
                
                return_code = process.poll()
                print(f"全量数据更新脚本执行完毕，返回码: {return_code}")
                return return_code == 0

        except Exception as e:
            print(f"运行装备分类脚本时发生错误: {e}")
            traceback.print_exc()
            return False

    def download_equipment_icons(self, base_dir=None):
        """
        调用高清图标下载模块来下载装备图标。
        """
        if not has_item_downloader:
            print("错误: 装备图标下载器模块不可用。")
            return False
            
        base_dir = base_dir or self.base_dir
        print("\n--- 开始下载/更新装备图标 ---")
        try:
            # 将下载器的日志重定向到我们的GUI日志
            set_downloader_logger(print) 
            success_count, fail_count = download_item_icons(base_dir=base_dir)
            print(f"装备图标下载完成。成功: {success_count}, 失败: {fail_count}")
            return True
        except Exception as e:
            print(f"下载装备图标时发生错误: {e}")
            traceback.print_exc()
            return False

    def get_equipment_data(self, force_update=False, download_icons=False):
        """获取装备数据(新版工作流)"""
        print("\n--- 开始处理装备模块 ---")
        self.progress_info["status"] = "正在更新装备数据"

        # 步骤1: 运行全量数据更新
        # force_update 参数在这里的体现就是调用 full mode
        update_success = self.run_equipment_classifier(mode='full', base_dir=self.base_dir)
        
        if not update_success:
            print("装备数据全量更新失败。")
            return False
            
        print("装备数据全量更新成功。")
        
        # 步骤2: (可选) 下载图标
        if download_icons:
            icon_download_success = self.download_equipment_icons(base_dir=self.base_dir)
            if not icon_download_success:
                print("但装备图标下载过程中出现问题。")
                # 这里我们不返回False，因为主要的数据更新已成功
        
        return True
    
    def get_hero_data(self, force_update=False, download_icons=False):
        """获取英雄基础数据（不包含装备数据）"""
        if not has_hero:
            print("错误: 英雄模块不可用")
            return False
        
        print("开始获取英雄基础数据...")
        self.progress_info["status"] = "正在获取英雄基础数据"
        
        try:
            # 调用统一的英雄数据获取方法，跳过装备数据
            success = self.get_hero_equipment_data(
                force_update=force_update,
                download_icons=False,  # 英雄模块不再处理图标下载
                skip_equips=True,  # 跳过装备数据，只获取英雄基础数据
                data_key="英雄"
            )

            # 如果成功且需要下载图标，调用高清图标下载
            if success and download_icons:
                print("开始下载英雄高清图标...")
                self.progress_info["status"] = "正在下载英雄高清图标"
                try:
                    from 高清图标下载 import download_hero_icons
                    icon_success = download_hero_icons(base_dir=self.base_dir)
                    if icon_success:
                        print("英雄高清图标下载完成")
                    else:
                        print("英雄高清图标下载失败")
                except Exception as e:
                    print(f"下载英雄高清图标时出错: {e}")

            return success
        except Exception as e:
            print(f"获取英雄基础数据时出错: {e}")
            traceback.print_exc()
            return False

    def get_hero_equipment_data(self, force_update=False, download_icons=False, skip_equips=False, data_key="英雄装备"):
        """获取英雄装备数据（可选择是否包含装备数据）
        
        Args:
            force_update: 是否强制更新
            download_icons: 是否下载图标
            skip_equips: 是否跳过装备数据（True=只获取英雄基础数据，False=获取完整数据）
            data_key: 数据存储的键名
        """
        if not has_hero_equips:
            print("错误: 英雄装备模块不可用")
            return False
        
        data_type = "英雄基础数据" if skip_equips else "英雄装备数据"
        print(f"开始获取{data_type}...")
        self.progress_info["status"] = f"正在获取{data_type}"
        
        try:
            # 调用英雄装备爬虫，根据skip_equips参数决定获取哪些数据
            success = run_hero_equipment_scraper(
                max_concurrent=3,
                base_dir=self.base_dir,
                skip_equips=skip_equips,  # 传递跳过装备参数
                download_icons=download_icons,
                fetch_heroes=True,  # 重新获取英雄数据
                gui=False,
                min_delay=0.5,
                max_delay=1.0,
                use_latest=True,
                force_update=force_update
            )
            
            # 验证数据是否成功获取
            mapping_file = os.path.join(self.base_dir, "映射文件", "英雄装备映射.json")
            if success and os.path.exists(mapping_file):
                try:
                    with open(mapping_file, 'r', encoding='utf-8') as f:
                        hero_data = json.load(f)
                    
                    # 根据数据类型存储到相应的键中
                    if skip_equips:
                        # 只获取英雄基础数据时，过滤掉装备相关字段
                        filtered_data = {}
                        for hero_name, hero_info in hero_data.items():
                            filtered_data[hero_name] = {
                                key: value for key, value in hero_info.items() 
                                if key not in ["data_path"]  # 过滤掉装备数据路径
                            }
                        self.data[data_key] = filtered_data
                    else:
                        # 获取完整数据
                        self.data[data_key] = hero_data
                    
                    print(f"成功获取{data_type}，共 {len(hero_data)} 个英雄")
                    return True
                    
                except Exception as e:
                    print(f"读取英雄数据映射失败: {e}")
                    return False
            else:
                if not success:
                    print(f"爬取{data_type}失败")
                else:
                    print(f"未找到英雄数据映射文件")
                return False
                
        except Exception as e:
            print(f"获取{data_type}时出错: {e}")
            traceback.print_exc()
            return False
    
    def get_comp_data(self, force_update=False, download_icons=False):
        """获取阵容数据

        注意：阵容模块暂不支持图标下载功能，download_icons参数被忽略
        """
        if not has_comp:
            print("错误: 阵容模块不可用")
            return False

        print("开始获取阵容数据...")
        self.progress_info["status"] = "正在获取阵容数据"
        
        # 修改调用方式，确保传递force_update参数
        success, data, mapping = get_comp_data(
            base_dir=self.base_dir,
            clean_old_data=force_update  # 使用force_update参数作为clean_old_data
        )
        
        if success:
            self.data["阵容"]["数据"] = data
            self.data["阵容"]["映射"] = mapping
            print(f"成功获取 {len(data)} 条阵容数据")
        else:
            print("获取阵容数据失败")

        return success

    def get_powerup_data(self, force_update=False, download_icons=False):
        """获取果实数据

        Args:
            force_update: 是否强制更新数据
            download_icons: 是否下载图标（果实模块暂不支持）
        """
        if not has_powerup:
            print("错误: 果实评级模块不可用")
            return False

        print("开始获取果实数据...")
        self.progress_info["status"] = "正在获取果实数据"

        try:
            # 切换到正确的工作目录
            original_cwd = os.getcwd()
            os.chdir(self.base_dir)

            # 运行果实爬取程序
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(powerup_main())
                success = True
                print("果实数据获取成功")
            except Exception as e:
                print(f"果实数据获取失败: {e}")
                traceback.print_exc()
                success = False
            finally:
                loop.close()

            # 恢复工作目录
            os.chdir(original_cwd)

            if success:
                # 检查生成的文件
                powerup_mapping_file = os.path.join(self.base_dir, "映射文件", "果实映射.json")
                powerup_data_dir = os.path.join(self.base_dir, "果实数据")

                if os.path.exists(powerup_mapping_file) and os.path.exists(powerup_data_dir):
                    self.data["果实"]["映射文件"] = powerup_mapping_file
                    self.data["果实"]["数据目录"] = powerup_data_dir
                    print("果实数据文件验证成功")
                else:
                    print("警告: 果实数据文件未找到")
                    success = False

            return success

        except Exception as e:
            print(f"获取果实数据时出错: {e}")
            traceback.print_exc()
            # 确保恢复工作目录
            try:
                os.chdir(original_cwd)
            except:
                pass
            return False
    
    def collect_selected_data(self, selected_modules, force_update=False, download_icons=False):
        """收集所选模块的数据
        
        Args:
            selected_modules: 字典，键为模块名，值为是否选中
            force_update: 是否强制更新数据
            download_icons: 是否下载图标
        """
        print("=" * 50)
        print("开始收集所选云顶之奕数据")
        print("=" * 50)
        
        selected_count = sum(1 for m, selected in selected_modules.items() if selected and self.modules_available.get(m, False))
        if selected_count == 0:
            print("没有选择任何可用模块，无法收集数据")
            return {}
        
        print(f"已选择 {selected_count} 个模块进行数据收集")
        print(f"配置: 强制更新={force_update}, 下载图标={download_icons}")
        
        start_time = time.time()
        self.progress_info["total"] = selected_count
        self.progress_info["current"] = 0
        
        results = {}
        for module, selected in selected_modules.items():
            if not selected or not self.modules_available.get(module, False):
                continue
                
            try:
                self.progress_info["message"] = f"正在处理模块: {module}"
                print(f"\n开始处理模块: {module}")
                
                if module == "海克斯":
                    results[module] = self.get_hextech_data(force_update, download_icons)
                elif module == "羁绊":
                    results[module] = self.get_trait_data(force_update, download_icons)
                elif module == "装备":
                    results[module] = self.get_equipment_data(force_update, download_icons)
                elif module == "英雄":
                    results[module] = self.get_hero_data(force_update, download_icons)
                elif module == "英雄装备":
                    results[module] = self.get_hero_equipment_data(force_update, download_icons)
                elif module == "阵容":
                    results[module] = self.get_comp_data(force_update, download_icons)
                elif module == "果实":
                    results[module] = self.get_powerup_data(force_update, download_icons)
                
                self.progress_info["current"] += 1
                
            except Exception as e:
                print(f"获取 {module} 数据时出错: {e}")
                traceback.print_exc()
                results[module] = False
                self.progress_info["current"] += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 显示结果统计
        success_count = sum(1 for r in results.values() if r)
        
        print("\n" + "=" * 50)
        print(f"数据收集完成: 成功 {success_count}/{selected_count} 个模块")
        print(f"总耗时: {duration:.2f} 秒")
        
        # 详细结果
        print("\n模块状态:")
        for module, success in results.items():
            status = "成功" if success else "失败"
            print(f"  {module}: {status}")
        
        print("=" * 50)
        self.progress_info["status"] = "完成"
        self.progress_info["message"] = f"成功 {success_count}/{selected_count} 个模块"
        
        return results
    
    def save_all_data(self, output_file="云顶数据汇总.json"):
        """将所有收集到的数据保存到一个汇总文件中"""
        try:
            output_path = os.path.join(self.base_dir, output_file)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            
            print(f"已将所有数据保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存数据时出错: {e}")
            traceback.print_exc()
            return False


class DataCollectorGUI:
    """云顶之奕数据收集器图形界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("云顶之奕数据收集工具")
        self.root.geometry("900x1100")  # 从650增加到975（增加二分之一）
        
        # 创建数据收集器实例
        self.collector = DataCollector()
        
        # 创建线程控制变量
        self.running = False
        self.thread = None
        
        # 创建界面元素
        self.create_widgets()
        
        # 设置默认选中项
        self.set_default_selections()
        
        # 重定向标准输出到日志框
        self.redirect_stdout()
    
    def create_widgets(self):
        """创建界面元素"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建数据保存目录选择
        dir_frame = ttk.LabelFrame(main_frame, text="数据保存目录", padding=5)
        dir_frame.pack(fill=tk.X, pady=5)
        
        self.dir_var = tk.StringVar(value=self.collector.base_dir)
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=70)
        dir_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(dir_frame, text="浏览...", command=self.browse_dir)
        browse_btn.pack(side=tk.RIGHT, padx=5)
        
        # 创建模块选择区域
        modules_frame = ttk.LabelFrame(main_frame, text="选择要运行的模块 (批量自动化)", padding=10)
        modules_frame.pack(fill=tk.X, pady=5)
        
        # 创建复选框
        self.module_vars = {}
        
        # 创建两列模块选择
        left_frame = ttk.Frame(modules_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_frame = ttk.Frame(modules_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 左侧模块
        left_modules = ["海克斯", "羁绊", "装备"]
        for module in left_modules:
            var = tk.BooleanVar(value=False)
            self.module_vars[module] = var
            
            check = ttk.Checkbutton(
                left_frame, 
                text=f"{module} 数据",
                variable=var,
                state=tk.NORMAL if self.collector.modules_available.get(module, False) else tk.DISABLED
            )
            check.pack(anchor=tk.W, pady=3)
        
        # 右侧模块
        right_modules = ["英雄", "英雄装备", "阵容", "果实"]
        for module in right_modules:
            var = tk.BooleanVar(value=False)
            self.module_vars[module] = var
            
            check = ttk.Checkbutton(
                right_frame, 
                text=f"{module} 数据",
                variable=var,
                state=tk.NORMAL if self.collector.modules_available.get(module, False) else tk.DISABLED
            )
            check.pack(anchor=tk.W, pady=3)
        
        # 创建选项区域
        options_frame = ttk.LabelFrame(main_frame, text="爬取选项", padding=5)
        options_frame.pack(fill=tk.X, pady=5)
        
        # 强制重新爬取选项
        self.force_var = tk.BooleanVar(value=True)
        force_check = ttk.Checkbutton(options_frame, text="强制重新爬取数据(忽略已有数据)", variable=self.force_var)
        force_check.pack(anchor=tk.W, pady=3)
        
        # 下载图标选项
        self.icon_var = tk.BooleanVar(value=False)
        icon_check = ttk.Checkbutton(options_frame, text="下载图标", variable=self.icon_var)
        icon_check.pack(anchor=tk.W, pady=3)
        
        # 新增：数据库重建选项
        self.rebuild_db_var = tk.BooleanVar(value=True)
        rebuild_db_check = ttk.Checkbutton(
            options_frame, 
            text="爬取完成后自动重建数据库", 
            variable=self.rebuild_db_var,
            state=tk.NORMAL if has_database_builder else tk.DISABLED
        )
        rebuild_db_check.pack(anchor=tk.W, pady=3)
        
        # --- 新增: 装备手动管理区域 ---
        equip_manual_frame = ttk.LabelFrame(main_frame, text="装备分类管理 (手动)", padding=10)
        equip_manual_frame.pack(fill=tk.X, pady=5)
        
        manual_intro_label = ttk.Label(
            equip_manual_frame, 
            text="用于新增/删除装备，或修改装备所属分类。会打开一个独立的控制台窗口进行操作。",
            wraplength=700, # 自动换行
            justify=tk.LEFT
        )
        manual_intro_label.pack(anchor=tk.W, pady=3, fill=tk.X)
        
        self.manual_classify_button = ttk.Button(
            equip_manual_frame, 
            text="启动交互式分类工具...", 
            command=self.run_interactive_classifier
        )
        self.manual_classify_button.pack(anchor=tk.W, pady=5)
        
        # 新增：制品库配置区域
        upload_frame = ttk.LabelFrame(main_frame, text="制品库配置", padding=5)
        upload_frame.pack(fill=tk.X, pady=5)
        
        # CODING_TOKEN状态显示
        token_status_frame = ttk.Frame(upload_frame)
        token_status_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(token_status_frame, text="CODING_TOKEN状态:").pack(side=tk.LEFT, padx=5)
        
        coding_token = os.getenv('CODING_TOKEN')
        if coding_token:
            token_status = "已设置 ✓"
            token_color = "green"
        else:
            token_status = "未设置 ✗"
            token_color = "red"
            
        self.token_status_label = ttk.Label(token_status_frame, text=token_status, foreground=token_color)
        self.token_status_label.pack(side=tk.LEFT, padx=5)
        
        # 制品库配置信息
        config_info_frame = ttk.Frame(upload_frame)
        config_info_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(config_info_frame, text=f"制品库: {REPO_CONFIG['repo']}").pack(side=tk.LEFT, padx=5)
        ttk.Label(config_info_frame, text=f"包: {REPO_CONFIG['package']}").pack(side=tk.LEFT, padx=10)
        
        # 创建按钮区域
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        # 第一行按钮
        buttons_row1 = ttk.Frame(buttons_frame)
        buttons_row1.pack(fill=tk.X, pady=2)
        
        self.start_button = ttk.Button(buttons_row1, text="开始爬取", command=self.start_collection)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(buttons_row1, text="停止爬取", command=self.stop_collection, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        select_all_button = ttk.Button(buttons_row1, text="全选", command=self.select_all)
        select_all_button.pack(side=tk.LEFT, padx=5)
        
        deselect_all_button = ttk.Button(buttons_row1, text="全不选", command=self.deselect_all)
        deselect_all_button.pack(side=tk.LEFT, padx=5)
        
        # 第二行按钮 - 数据库和上传操作
        buttons_row2 = ttk.Frame(buttons_frame)
        buttons_row2.pack(fill=tk.X, pady=2)
        
        self.rebuild_db_button = ttk.Button(
            buttons_row2, 
            text="重建数据库", 
            command=self.manual_rebuild_database,
            state=tk.NORMAL if has_database_builder else tk.DISABLED
        )
        self.rebuild_db_button.pack(side=tk.LEFT, padx=5)
        
        self.upload_db_button = ttk.Button(
            buttons_row2, 
            text="上传数据库", 
            command=self.upload_database,
            state=tk.NORMAL if coding_token else tk.DISABLED
        )
        self.upload_db_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新token状态按钮
        refresh_token_button = ttk.Button(buttons_row2, text="刷新Token状态", command=self.refresh_token_status)
        refresh_token_button.pack(side=tk.LEFT, padx=5)
        
        # 创建日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 添加日志滚动控制
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, side=tk.TOP, pady=2)
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = ttk.Checkbutton(
            log_control_frame, 
            text="自动滚动日志", 
            variable=self.auto_scroll_var
        )
        auto_scroll_check.pack(side=tk.LEFT, padx=5)
        
        scroll_to_top_btn = ttk.Button(
            log_control_frame, 
            text="滚动到顶部", 
            command=lambda: self.log_text.see('1.0')
        )
        scroll_to_top_btn.pack(side=tk.LEFT, padx=5)
        
        scroll_to_bottom_btn = ttk.Button(
            log_control_frame, 
            text="滚动到底部", 
            command=lambda: self.log_text.see(tk.END)
        )
        scroll_to_bottom_btn.pack(side=tk.LEFT, padx=5)
        
        clear_log_btn = ttk.Button(
            log_control_frame, 
            text="清空日志", 
            command=lambda: self.log_text.delete('1.0', tk.END)
        )
        clear_log_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 创建进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5)
        
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.pack(pady=5)
    
    def set_default_selections(self):
        """设置默认选中的模块"""
        default_modules = ["英雄", "英雄装备", "海克斯", "阵容", "果实"]
        
        for module in default_modules:
            if module in self.module_vars and self.collector.modules_available.get(module, False):
                self.module_vars[module].set(True)
        
        # 默认选中装备选项
        if "装备" in self.module_vars and self.collector.modules_available.get("装备", False):
            self.module_vars["装备"].set(True)
    
    def browse_dir(self):
        """浏览选择数据保存目录"""
        dir_path = filedialog.askdirectory(initialdir=self.dir_var.get())
        if dir_path:
            self.dir_var.set(dir_path)
            # 更新数据收集器的基础目录
            self.collector.base_dir = dir_path
    
    def select_all(self):
        """选择所有可用模块"""
        for module, var in self.module_vars.items():
            if self.collector.modules_available.get(module, False):
                var.set(True)
    
    def deselect_all(self):
        """取消选择所有模块"""
        for var in self.module_vars.values():
            var.set(False)
    
    def get_selected_modules(self):
        """获取用户选择的模块"""
        selected = {}
        for module, var in self.module_vars.items():
            selected[module] = var.get()
        return selected
    
    def start_collection(self):
        """启动数据收集线程"""
        # 检查是否有选择模块
        selected_modules = self.get_selected_modules()
        if not any(selected_modules.values()):
            messagebox.showwarning("警告", "请至少选择一个要爬取的模块！")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        # 更新收集器的基础目录
        self.collector.base_dir = self.dir_var.get()
        
        # 获取选项
        force_update = self.force_var.get()
        download_icons = self.icon_var.get()
        
        # 设置运行状态
        self.running = True
        
        # 创建并启动线程
        self.thread = threading.Thread(
            target=self.run_collection_thread,
            args=(selected_modules, force_update, download_icons),
            daemon=True
        )
        self.thread.start()
        
        # 启动进度更新
        self.update_progress()
    
    def stop_collection(self):
        """停止数据收集"""
        if messagebox.askyesno("确认", "确定要停止当前爬取过程吗？\n注意：已经启动的爬虫可能无法立即停止。"):
            self.running = False
            self.log_text.insert(tk.END, "\n用户中断了爬取过程。\n")
            self.status_var.set("已中断")
            
            # 恢复UI状态
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def run_collection_thread(self, selected_modules, force_update, download_icons):
        """运行数据收集线程"""
        try:
            # 开始收集数据
            results = self.collector.collect_selected_data(
                selected_modules, 
                force_update=force_update,
                download_icons=download_icons
            )
            
            # 检查是否被用户中断
            if not self.running:
                return
            
            # 在主线程中执行UI更新
            self.root.after(0, self.collection_finished, results)
            
        except Exception as e:
            # 记录错误
            error_msg = f"数据收集过程中发生错误: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            
            # 在主线程中显示错误
            self.root.after(0, self.show_error, str(e))
    
    def collection_finished(self, results):
        """数据收集完成处理（修改版，支持自动数据库重建）"""
        # 恢复UI状态
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        # 显示完成消息
        success_count = sum(1 for r in results.values() if r)
        total_count = len(results)
        
        if success_count == total_count:
            self.status_var.set(f"数据收集完成：{success_count}/{total_count}")
            
            # 检查是否需要自动重建数据库
            if self.rebuild_db_var.get() and has_database_builder:
                print("开始自动重建数据库...")
                self.status_var.set("数据收集完成，正在重建数据库...")
                
                # 在线程中执行数据库重建
                threading.Thread(target=self._auto_rebuild_database_thread, args=(results,), daemon=True).start()
            else:
                # 不重建数据库，直接显示完成消息
                messagebox.showinfo("完成", f"所有选定的模块数据爬取完成！\n成功：{success_count}/{total_count}")
        else:
            self.status_var.set(f"部分模块爬取成功：{success_count}/{total_count}")
            
            # 构建失败模块列表
            failed_modules = []
            for module, success in results.items():
                if not success:
                    failed_modules.append(module)
            
            failed_str = ", ".join(failed_modules)
            
            # 检查是否需要自动重建数据库（即使部分失败）
            if self.rebuild_db_var.get() and has_database_builder and success_count > 0:
                result = messagebox.askyesno(
                    "部分成功", 
                    f"部分模块数据爬取失败！\n成功：{success_count}/{total_count}\n失败模块：{failed_str}\n\n是否仍要重建数据库？"
                )
                if result:
                    print("开始自动重建数据库...")
                    self.status_var.set("部分模块成功，正在重建数据库...")
                    threading.Thread(target=self._auto_rebuild_database_thread, args=(results,), daemon=True).start()
                else:
                    messagebox.showwarning("部分成功", f"数据收集部分完成，未重建数据库。\n\n详细信息请查看日志。")
            else:
                messagebox.showwarning("部分成功", 
                                     f"部分模块数据爬取失败！\n成功：{success_count}/{total_count}\n\n失败模块：{failed_str}\n\n请查看日志了解详情。")
    
    def _auto_rebuild_database_thread(self, collection_results):
        """自动数据库重建线程"""
        try:
            # 更新收集器的基础目录
            self.collector.base_dir = self.dir_var.get()
            
            success = self.collector.rebuild_database()
            
            # 在主线程中更新UI
            self.root.after(0, self._on_auto_database_rebuild_finished, success, collection_results)
            
        except Exception as e:
            error_msg = f"自动数据库重建过程中发生错误: {str(e)}"
            print(error_msg)
            self.root.after(0, self._on_auto_database_rebuild_finished, False, collection_results, error_msg)
    
    def _on_auto_database_rebuild_finished(self, success, collection_results, error_msg=None):
        """自动数据库重建完成后的处理"""
        success_count = sum(1 for r in collection_results.values() if r)
        total_count = len(collection_results)
        
        if success:
            self.status_var.set("数据收集和数据库重建完成")
            messagebox.showinfo("完成", f"数据爬取和数据库重建完成！\n爬取成功：{success_count}/{total_count}\n数据库重建：成功")
        else:
            self.status_var.set("数据库重建失败")
            error_text = f"数据爬取完成但数据库重建失败！\n爬取成功：{success_count}/{total_count}\n数据库重建：失败"
            if error_msg:
                error_text += f"\n\n错误信息：{error_msg}"
            messagebox.showerror("重建失败", error_text)
    
    def show_error(self, error_msg):
        """显示错误消息"""
        # 恢复UI状态
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
        # 设置状态
        self.status_var.set("爬取过程中发生错误")
        
        # 显示错误对话框
        messagebox.showerror("错误", f"爬取过程中发生错误：\n{error_msg}\n\n详细信息请查看日志。")
    
    def update_progress(self):
        """更新进度显示"""
        if self.running:
            # 更新进度条
            if self.collector.progress_info["total"] > 0:
                progress = (self.collector.progress_info["current"] / self.collector.progress_info["total"]) * 100
                self.progress_var.set(progress)
            
            # 更新状态文本
            status = self.collector.progress_info["status"]
            message = self.collector.progress_info["message"]
            current = self.collector.progress_info["current"]
            total = self.collector.progress_info["total"]
            
            if total > 0:
                self.status_var.set(f"{status} - {message} ({current}/{total})")
            else:
                self.status_var.set(f"{status} - {message}")
            
            # 继续更新
            self.root.after(200, self.update_progress)
    
    def redirect_stdout(self):
        """重定向标准输出到日志文本框"""
        class TextRedirector:
            def __init__(self, text_widget, auto_scroll_var):
                self.text_widget = text_widget
                self.auto_scroll_var = auto_scroll_var
                self.buffer = ""
            
            def write(self, string):
                self.buffer += string
                if string.endswith('\n') or len(self.buffer) > 200:
                    self.text_widget.insert(tk.END, self.buffer)
                    # 只有当自动滚动选项开启时才自动滚动
                    if self.auto_scroll_var.get():
                        self.text_widget.see(tk.END)
                    self.buffer = ""
            
            def flush(self):
                if self.buffer:
                    self.text_widget.insert(tk.END, self.buffer)
                    # 只有当自动滚动选项开启时才自动滚动
                    if self.auto_scroll_var.get():
                        self.text_widget.see(tk.END)
                    self.buffer = ""
        
        # 保存原始标准输出
        self.original_stdout = sys.stdout
        
        # 重定向到文本框，传递自动滚动变量
        sys.stdout = TextRedirector(self.log_text, self.auto_scroll_var)
    
    def refresh_token_status(self):
        """刷新CODING_TOKEN状态"""
        coding_token = os.getenv('CODING_TOKEN')
        if coding_token:
            token_status = "已设置 ✓"
            token_color = "green"
            self.upload_db_button.config(state=tk.NORMAL)
        else:
            token_status = "未设置 ✗"
            token_color = "red"
            self.upload_db_button.config(state=tk.DISABLED)
            
        self.token_status_label.config(text=token_status, foreground=token_color)
        print(f"CODING_TOKEN状态: {token_status}")
    
    def manual_rebuild_database(self):
        """手动重建数据库"""
        if not has_database_builder:
            messagebox.showerror("错误", "数据库建立模块不可用，无法重建数据库！")
            return
            
        if messagebox.askyesno("确认", "确定要重建数据库吗？\n这将删除现有数据库并重新创建。"):
            # 禁用相关按钮
            self.rebuild_db_button.config(state=tk.DISABLED)
            self.upload_db_button.config(state=tk.DISABLED)
            
            # 在线程中执行重建
            threading.Thread(target=self._rebuild_database_thread, daemon=True).start()
    
    def _rebuild_database_thread(self):
        """数据库重建线程"""
        try:
            # 更新收集器的基础目录
            self.collector.base_dir = self.dir_var.get()
            
            success = self.collector.rebuild_database()
            
            # 在主线程中更新UI
            self.root.after(0, self._on_database_rebuild_finished, success)
            
        except Exception as e:
            error_msg = f"数据库重建过程中发生错误: {str(e)}"
            print(error_msg)
            self.root.after(0, self._on_database_rebuild_finished, False, error_msg)
    
    def _on_database_rebuild_finished(self, success, error_msg=None):
        """数据库重建完成后的处理"""
        # 恢复按钮状态
        self.rebuild_db_button.config(state=tk.NORMAL if has_database_builder else tk.DISABLED)
        
        coding_token = os.getenv('CODING_TOKEN')
        self.upload_db_button.config(state=tk.NORMAL if coding_token else tk.DISABLED)
        
        if success:
            self.status_var.set("数据库重建完成")
            messagebox.showinfo("完成", "数据库重建成功！")
        else:
            self.status_var.set("数据库重建失败")
            error_text = f"数据库重建失败！\n\n{error_msg}" if error_msg else "数据库重建失败！"
            messagebox.showerror("错误", error_text)
    
    def upload_database(self):
        """上传数据库到制品库"""
        # 检查CODING_TOKEN
        if not os.getenv('CODING_TOKEN'):
            messagebox.showerror("错误", "未设置CODING_TOKEN环境变量！\n请设置后重新启动程序。")
            return
            
        # 检查数据库文件是否存在
        db_file_path = os.path.join(self.dir_var.get(), DATABASE_CONFIG['complete_database']['db_file'])
        if not os.path.exists(db_file_path):
            result = messagebox.askyesno(
                "数据库文件不存在", 
                f"数据库文件不存在: {db_file_path}\n\n是否先重建数据库？"
            )
            if result:
                self.manual_rebuild_database()
            return
        
        if messagebox.askyesno("确认", "确定要上传数据库到制品库吗？"):
            # 禁用相关按钮
            self.upload_db_button.config(state=tk.DISABLED)
            self.rebuild_db_button.config(state=tk.DISABLED)
            
            # 在线程中执行上传
            threading.Thread(target=self._upload_database_thread, daemon=True).start()
    
    def _upload_database_thread(self):
        """数据库上传线程"""
        try:
            # 更新收集器的基础目录
            self.collector.base_dir = self.dir_var.get()
            
            success = self.collector.upload_database()
            
            # 在主线程中更新UI
            self.root.after(0, self._on_database_upload_finished, success)
            
        except Exception as e:
            error_msg = f"数据库上传过程中发生错误: {str(e)}"
            print(error_msg)
            self.root.after(0, self._on_database_upload_finished, False, error_msg)
    
    def _on_database_upload_finished(self, success, error_msg=None):
        """数据库上传完成后的处理"""
        # 恢复按钮状态
        coding_token = os.getenv('CODING_TOKEN')
        self.upload_db_button.config(state=tk.NORMAL if coding_token else tk.DISABLED)
        self.rebuild_db_button.config(state=tk.NORMAL if has_database_builder else tk.DISABLED)
        
        if success:
            self.status_var.set("数据库上传完成")
            show_upload_message("上传成功", "数据库上传到制品库成功！", messagebox.INFO)
        else:
            self.status_var.set("数据库上传失败")
            error_text = f"数据库上传失败！\n\n{error_msg}" if error_msg else "数据库上传失败！"
            show_upload_message("上传失败", error_text, messagebox.ERROR)

    def run_interactive_classifier(self):
        """启动交互式装备分类工具"""
        print("\n" + "=" * 50)
        print("请求启动交互式装备分类工具...")
        self.status_var.set("正在启动交互式工具...")
        
        # 在一个新线程中运行，以防主GUI卡顿
        # 虽然子进程本身不阻塞，但启动过程可能耗时
        threading.Thread(
            target=self._run_interactive_classifier_thread,
            daemon=True
        ).start()

    def _run_interactive_classifier_thread(self):
        """运行交互式分类的线程"""
        try:
            # 更新收集器的基础目录
            self.collector.base_dir = self.dir_var.get()
            success = self.collector.run_equipment_classifier(mode='interactive')
            
            # 在主线程中更新UI状态
            if success:
                self.root.after(0, lambda: self.status_var.set("交互式工具已启动，请在新窗口中操作。"))
            else:
                self.root.after(0, lambda: self.status_var.set("交互式工具启动失败，请查看日志。"))

        except Exception as e:
            print(f"启动交互式工具时出错: {e}")
            self.root.after(0, lambda: self.status_var.set("启动交互式工具时出错。"))

def main():
    """程序入口"""
    # 检查是否安装了必要的库
    try:
        import requests
    except ImportError:
        print("错误：缺少 requests 库。请运行 'pip install requests' 安装。")
        sys.exit(1)
    
    # 检查数据库建立模块
    if not has_database_builder:
        print("警告：数据库建立模块不可用，将无法重建数据库。")
    
    # 创建主窗口
    root = tk.Tk()
    
    # 创建并运行GUI
    app = DataCollectorGUI(root)
    
    # 显示启动信息
    print("=" * 50)
    print("云顶之奕数据收集工具已启动")
    print("=" * 50)
    print(f"数据库建立模块: {'可用' if has_database_builder else '不可用'}")
    
    coding_token = os.getenv('CODING_TOKEN')
    print(f"CODING_TOKEN状态: {'已设置' if coding_token else '未设置'}")
    
    if not coding_token:
        print("提示：如需上传到制品库，请设置 CODING_TOKEN 环境变量")
    
    print("=" * 50)
    
    # 启动主循环
    root.mainloop()
    
    # 恢复标准输出
    sys.stdout = app.original_stdout

if __name__ == "__main__":
    main() 