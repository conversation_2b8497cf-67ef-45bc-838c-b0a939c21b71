<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import { emit as tauriEmit, listen } from '@tauri-apps/api/event'

// 定义组件的 props
interface Props {
  isMainWindowMinimized?: boolean
  ocrStatus?: any
  pythonRunning?: boolean
  autoMode?: boolean
  isLoading?: boolean
  statusMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  isMainWindowMinimized: false,
  pythonRunning: false,
  autoMode: false,
  isLoading: false,
  statusMessage: '正在初始化...'
})

// 响应式数据
const floatingWindowExists = ref(false)
const floatingWindowVisible = ref(false)

// 检查悬浮窗是否存在
const checkFloatingWindowExists = async () => {
  try {
    const exists = await invoke<boolean>('is_floating_ocr_window_exists')
    floatingWindowExists.value = exists
    console.log('悬浮窗存在状态:', exists)
    return exists
  } catch (error) {
    console.error('检查悬浮窗存在状态失败:', error)
    return false
  }
}

// 创建悬浮窗
const createFloatingWindow = async () => {
  try {
    console.log('创建悬浮窗...')
    await invoke('create_floating_ocr_window')
    floatingWindowExists.value = true
    console.log('悬浮窗创建成功')
  } catch (error) {
    console.error('创建悬浮窗失败:', error)
    throw error
  }
}

// 显示悬浮窗
const showFloatingWindow = async () => {
  try {
    // 如果悬浮窗不存在，先创建
    if (!floatingWindowExists.value) {
      await createFloatingWindow()
    }
    
    console.log('显示悬浮窗...')
    await invoke('show_floating_ocr_window')
    floatingWindowVisible.value = true
    console.log('悬浮窗已显示')
    
    // 同步状态到悬浮窗
    await syncStatusToFloatingWindow()
  } catch (error) {
    console.error('显示悬浮窗失败:', error)
  }
}

// 隐藏悬浮窗
const hideFloatingWindow = async () => {
  try {
    console.log('隐藏悬浮窗...')
    await invoke('hide_floating_ocr_window')
    floatingWindowVisible.value = false
    console.log('悬浮窗已隐藏')
  } catch (error) {
    console.error('隐藏悬浮窗失败:', error)
  }
}

// 切换悬浮窗显示状态
const toggleFloatingWindow = async () => {
  try {
    console.log('切换悬浮窗显示状态...')
    const isVisible = await invoke<boolean>('toggle_floating_ocr_window')
    floatingWindowVisible.value = isVisible
    
    if (isVisible) {
      floatingWindowExists.value = true
      await syncStatusToFloatingWindow()
    }
    
    console.log('悬浮窗状态已切换:', isVisible ? '显示' : '隐藏')
  } catch (error) {
    console.error('切换悬浮窗状态失败:', error)
  }
}

// 同步状态到悬浮窗
const syncStatusToFloatingWindow = async () => {
  try {
    if (!floatingWindowExists.value) return
    
    const statusData = {
      pythonRunning: props.pythonRunning,
      autoMode: props.autoMode,
      isLoading: props.isLoading,
      statusMessage: props.statusMessage
    }
    
    console.log('同步状态到悬浮窗:', statusData)
    await tauriEmit('ocr-status-update', statusData)
  } catch (error) {
    console.error('同步状态到悬浮窗失败:', error)
  }
}

// 监听主窗口最小化状态变化
watch(() => props.isMainWindowMinimized, async (isMinimized) => {
  console.log('主窗口最小化状态变化:', isMinimized)

  if (!isMinimized) {
    // 主窗口展开时隐藏悬浮窗
    await hideFloatingWindow()
  }
  // 移除自动显示逻辑，改为手动点击控制
})

// 监听OCR状态变化，同步到悬浮窗
watch([
  () => props.pythonRunning,
  () => props.autoMode,
  () => props.isLoading,
  () => props.statusMessage
], async () => {
  if (floatingWindowVisible.value) {
    await syncStatusToFloatingWindow()
  }
}, { deep: true })

// 定义emits
const emit = defineEmits<{
  'floating-status-update': [status: {
    pythonRunning: boolean
    autoMode: boolean
    isLoading: boolean
  }]
}>()

// 组件挂载时初始化
onMounted(async () => {
  console.log('FloatingOCRManager 组件已挂载')

  // 检查悬浮窗是否已存在
  await checkFloatingWindowExists()

  // 监听悬浮窗状态更新事件
  try {
    await listen('floating-ocr-status-update', (event) => {
      console.log('收到悬浮窗状态更新:', event.payload)
      emit('floating-status-update', event.payload as any)
    })
    console.log('已设置悬浮窗状态更新监听器')
  } catch (error) {
    console.error('设置悬浮窗状态监听器失败:', error)
  }

  // 如果主窗口已经是最小化状态，显示悬浮窗
  if (props.isMainWindowMinimized) {
    await showFloatingWindow()
  }
})

// 组件卸载时清理
onBeforeUnmount(async () => {
  console.log('FloatingOCRManager 组件即将卸载')

  // 可选：关闭悬浮窗
  try {
    if (floatingWindowExists.value) {
      await invoke('close_floating_ocr_window')
    }
  } catch (error) {
    console.error('关闭悬浮窗失败:', error)
  }
})

// 暴露方法给父组件
defineExpose({
  toggleFloatingWindow,
  showFloatingWindow,
  hideFloatingWindow
})
</script>

<template>
  <!-- 这个组件主要用于逻辑控制，不需要渲染任何UI -->
  <div style="display: none;">
    <!-- 悬浮窗管理器 - 仅用于逻辑控制 -->
  </div>
</template>

<style scoped>
/* 无需样式，因为这个组件不渲染UI */
</style>
